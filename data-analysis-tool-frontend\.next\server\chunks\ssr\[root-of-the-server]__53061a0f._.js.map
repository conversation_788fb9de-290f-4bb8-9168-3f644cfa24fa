{"version": 3, "sources": [], "sections": [{"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/axios.ts"], "sourcesContent": ["import axios from \"axios\";\r\n\r\nconst axiosInstance = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:4000/api\",\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n  },\r\n  withCredentials: true,\r\n});\r\n\r\n// Add request interceptor to handle auth token\r\naxiosInstance.interceptors.request.use(\r\n  (config) => {\r\n    // You can add auth token here if needed\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add response interceptor to handle errors\r\naxiosInstance.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    if (error.code === \"ERR_NETWORK\") {\r\n      console.error(\r\n        \"Network error - Please check if the backend server is running\"\r\n      );\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default axiosInstance;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,gBAAgB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACjC,SAAS,iEAAmC;IAC5C,SAAS;QACP,gBAAgB;IAClB;IACA,iBAAiB;AACnB;AAEA,+CAA+C;AAC/C,cAAc,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;IACC,wCAAwC;IACxC,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,4CAA4C;AAC5C,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,IAAI,KAAK,eAAe;QAChC,QAAQ,KAAK,CACX;IAEJ;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/encodeDecode.ts"], "sourcesContent": ["import Hashids from \"hashids\";\r\n\r\nconst salt = process.env.SALT || \"rushan-salt\";\r\n\r\nconst hashids = new Hashids(salt, 12);\r\n\r\nconst encode = (id: number) => {\r\n  return hashids.encode(id);\r\n};\r\n\r\nconst decode = (hash: string) => {\r\n  const decodedNumberLike = hashids.decode(hash)[0];\r\n  const decoded =\r\n    typeof decodedNumberLike === \"bigint\"\r\n      ? decodedNumberLike < Number.MAX_SAFE_INTEGER\r\n        ? Number(decodedNumberLike)\r\n        : null\r\n      : typeof decodedNumberLike === \"number\"\r\n      ? decodedNumberLike\r\n      : null;\r\n  return decoded;\r\n};\r\n\r\nexport { encode, decode };\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,OAAO,QAAQ,GAAG,CAAC,IAAI,IAAI;AAEjC,MAAM,UAAU,IAAI,yIAAA,CAAA,UAAO,CAAC,MAAM;AAElC,MAAM,SAAS,CAAC;IACd,OAAO,QAAQ,MAAM,CAAC;AACxB;AAEA,MAAM,SAAS,CAAC;IACd,MAAM,oBAAoB,QAAQ,MAAM,CAAC,KAAK,CAAC,EAAE;IACjD,MAAM,UACJ,OAAO,sBAAsB,WACzB,oBAAoB,OAAO,gBAAgB,GACzC,OAAO,qBACP,OACF,OAAO,sBAAsB,WAC7B,oBACA;IACN,OAAO;AACT", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/api/form-builder.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\nimport { ContextType } from \"@/types\";\r\n\r\nconst getQuestionsEndPoint = (contextType: ContextType) => {\r\n  if (contextType === \"project\") return \"/questions\";\r\n  else if (contextType === \"template\") return \"/template-questions\";\r\n  else if (contextType === \"questionBlock\") return \"/question-blocks\";\r\n  throw new Error(\"Unsupported context type\");\r\n};\r\n\r\nconst fetchQuestions = async ({ projectId }: { projectId: number }) => {\r\n  const { data } = await axios.get(`/questions/${projectId}`);\r\n  return data.questions;\r\n};\r\n\r\nconst fetchTemplateQuestions = async ({\r\n  templateId,\r\n}: {\r\n  templateId: number;\r\n}) => {\r\n  const { data } = await axios.get(`/template-questions/${templateId}`);\r\n  return data.questions;\r\n};\r\n\r\n// const addQuestion = async ({\r\n//   contextType,\r\n//   contextId,\r\n//   dataToSend,\r\n//   position,\r\n// }: {\r\n//   contextType: ContextType;\r\n//   contextId: number;\r\n//   dataToSend: {\r\n//     label: string;\r\n//     isRequired: boolean;\r\n//     hint?: string;\r\n//     placeholder?: string;\r\n//     inputType: string;\r\n//     questionOptions?: {\r\n//       label: string;\r\n//       code: string;\r\n//       nextQuestionId?: number | null;\r\n//     }[];\r\n//   };\r\n//   position?: number;\r\n// }) => {\r\n//   // For question blocks, we don't need to include the contextId in the URL\r\n//   // The userId is taken from the authenticated user in the backend\r\n//   const url =\r\n//     contextType === \"questionBlock\"\r\n//       ? `${getQuestionsEndPoint(contextType)}`\r\n//       : `${getQuestionsEndPoint(contextType)}/${contextId}`;\r\n\r\n//   const { data } = await axios.post(url, {\r\n//     ...dataToSend,\r\n//     position: position || 1,\r\n//   });\r\n//   return data;\r\n// };\r\n\r\n// const addQuestion = async ({\r\n//   contextType,\r\n//   contextId,\r\n//   dataToSend,\r\n//   position,\r\n// }: {\r\n//   contextType: ContextType;\r\n//   contextId: number;\r\n//   dataToSend: {\r\n//     label: string;\r\n//     isRequired: boolean;\r\n//     hint?: string;\r\n//     placeholder?: string;\r\n//     inputType: string;\r\n//     questionOptions?: {\r\n//       label: string;\r\n//       code: string;\r\n//       nextQuestionId?: number | null;\r\n//     }[];\r\n//     file?: File;\r\n//   };\r\n//   position?: number;\r\n// }) => {\r\n//   // For question blocks, we don't need to include the contextId in the URL\r\n//   // The userId is taken from the authenticated user in the backend\r\n//   const url =\r\n//     contextType === \"questionBlock\"\r\n//       ? `${getQuestionsEndPoint(contextType)}`\r\n//       : `${getQuestionsEndPoint(contextType)}/${contextId}`;\r\n\r\n//   // Check if we need to send form data (for file upload)\r\n//   const hasFile = dataToSend.file instanceof File;\r\n\r\n//   if (hasFile) {\r\n//     // Create FormData for file upload\r\n//     const formData = new FormData();\r\n\r\n//     // Append all the regular data\r\n//     formData.append(\"label\", dataToSend.label);\r\n//     formData.append(\"isRequired\", dataToSend.isRequired.toString());\r\n//     formData.append(\"inputType\", dataToSend.inputType);\r\n\r\n//     if (dataToSend.hint) {\r\n//       formData.append(\"hint\", dataToSend.hint);\r\n//     }\r\n\r\n//     if (dataToSend.placeholder) {\r\n//       formData.append(\"placeholder\", dataToSend.placeholder);\r\n//     }\r\n\r\n//     formData.append(\"position\", (position || 1).toString());\r\n\r\n//     // Append the Excel file\r\n//     formData.append(\"file\", dataToSend.file as File);\r\n\r\n//     const { data } = await axios.post(url, formData, {\r\n//       headers: {\r\n//         \"Content-Type\": \"multipart/form-data\",\r\n//       },\r\n//     });\r\n\r\n//     return data;\r\n//   } else {\r\n//     // Regular JSON request (no file)\r\n//     const { data } = await axios.post(url, {\r\n//       label: dataToSend.label,\r\n//       isRequired: dataToSend.isRequired,\r\n//       hint: dataToSend.hint,\r\n//       placeholder: dataToSend.placeholder,\r\n//       inputType: dataToSend.inputType,\r\n//       questionOptions: dataToSend.questionOptions,\r\n//       position: position || 1,\r\n//     });\r\n\r\n//     return data;\r\n//   }\r\n// };\r\n\r\nconst addQuestion = async ({\r\n  contextType,\r\n  contextId,\r\n  dataToSend,\r\n  position,\r\n}: {\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  dataToSend: {\r\n    label: string;\r\n    isRequired: boolean;\r\n    hint?: string;\r\n    placeholder?: string;\r\n    inputType: string;\r\n    questionOptions?: {\r\n      label: string;\r\n      sublabel?: string;\r\n      code: string;\r\n      nextQuestionId?: number | null;\r\n    }[];\r\n    file?: File;\r\n  };\r\n  position?: number;\r\n}) => {\r\n  const url =\r\n    contextType === \"questionBlock\"\r\n      ? `${getQuestionsEndPoint(contextType)}`\r\n      : `${getQuestionsEndPoint(contextType)}/${contextId}`;\r\n\r\n  // Validate required fields\r\n  if (!dataToSend.label || !dataToSend.inputType) {\r\n    throw new Error(\"Label and inputType are required\");\r\n  }\r\n\r\n  // Check if this input type requires options\r\n  const needsOptions = [\"selectone\", \"selectmany\"].includes(\r\n    dataToSend.inputType\r\n  );\r\n  const hasFile = dataToSend.file instanceof File;\r\n  const hasOptions =\r\n    Array.isArray(dataToSend.questionOptions) &&\r\n    dataToSend.questionOptions.length > 0;\r\n\r\n  // Validate options based on input type and upload method\r\n  if (needsOptions && !hasFile && !hasOptions) {\r\n    throw new Error(\"Options are required for select input types\");\r\n  }\r\n\r\n  if (hasFile) {\r\n    const formData = new FormData();\r\n\r\n    // Add basic question data\r\n    formData.append(\"label\", dataToSend.label);\r\n    // Convert boolean to string in a way backend can parse\r\n    formData.append(\"isRequired\", dataToSend.isRequired ? \"true\" : \"false\");\r\n    formData.append(\"inputType\", dataToSend.inputType);\r\n    if (dataToSend.hint) formData.append(\"hint\", dataToSend.hint);\r\n    if (dataToSend.placeholder)\r\n      formData.append(\"placeholder\", dataToSend.placeholder);\r\n    // Convert number to string\r\n    formData.append(\"position\", String(position || 1));\r\n\r\n    // Add file with the correct field name\r\n    formData.append(\"file\", dataToSend.file as File);\r\n\r\n    // Important: Do NOT include questionOptions when uploading a file\r\n    // They will be parsed from the file on the server\r\n\r\n    try {\r\n      const { data } = await axios.post(url, formData, {\r\n        headers: {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n      });\r\n      return data;\r\n    } catch (error: any) {\r\n      console.error(\r\n        \"Upload error details:\",\r\n        error.response?.data || error.message\r\n      );\r\n      throw new Error(\r\n        `Failed to upload question with file: ${\r\n          error.response?.data?.message || error.message\r\n        }`\r\n      );\r\n    }\r\n  } else {\r\n    // Regular JSON request (no file)\r\n    try {\r\n      const { data } = await axios.post(url, {\r\n        label: dataToSend.label,\r\n        isRequired: dataToSend.isRequired,\r\n        hint: dataToSend.hint,\r\n        placeholder: dataToSend.placeholder,\r\n        inputType: dataToSend.inputType,\r\n        questionOptions: dataToSend.questionOptions,\r\n        position: position || 1,\r\n      });\r\n      return data;\r\n    } catch (error: any) {\r\n      console.error(\r\n        \"API error details:\",\r\n        error.response?.data || error.message\r\n      );\r\n      throw new Error(\r\n        `Failed to add question: ${\r\n          error.response?.data?.message || error.message\r\n        }`\r\n      );\r\n    }\r\n  }\r\n};\r\nconst deleteQuestion = async ({\r\n  contextType,\r\n  id,\r\n  projectId,\r\n}: {\r\n  contextType: ContextType;\r\n  id: number;\r\n  projectId: number;\r\n}) => {\r\n  const { data } = await axios.delete(\r\n    `${getQuestionsEndPoint(contextType)}/${id}?projectId=${projectId}`\r\n  );\r\n  return data;\r\n};\r\n\r\nconst duplicateQuestion = async ({\r\n  id,\r\n  contextType,\r\n  contextId,\r\n}: {\r\n  id: number;\r\n  contextType: ContextType;\r\n  contextId: number;\r\n}) => {\r\n  // For question blocks, we don't need to send the contextId in the body\r\n  // The userId is taken from the authenticated user in the backend\r\n  const requestBody =\r\n    contextType === \"questionBlock\"\r\n      ? {}\r\n      : contextType === \"project\"\r\n      ? { projectId: contextId }\r\n      : { templateId: contextId };\r\n\r\n  const { data } = await axios.post(\r\n    `${getQuestionsEndPoint(\r\n      contextType\r\n    )}/duplicate/${id}?projectId=${contextId}`,\r\n    requestBody\r\n  );\r\n\r\n  return data;\r\n};\r\n\r\nconst updateQuestion = async ({\r\n  id,\r\n  contextType,\r\n  dataToSend,\r\n  contextId,\r\n}: {\r\n  id: number;\r\n  contextType: ContextType;\r\n  dataToSend: {\r\n    label: string;\r\n    isRequired: boolean;\r\n    hint: string;\r\n    placeholder: string;\r\n    position?: number; // Optional position field to preserve question order\r\n    questionOptions?: {\r\n      label: string;\r\n      sublabel?: string;\r\n      code: string;\r\n      nextQuestionId?: number | null;\r\n    }[];\r\n  };\r\n  contextId: number;\r\n}) => {\r\n  const { data } = await axios.patch(\r\n    `${getQuestionsEndPoint(contextType)}/${id}?projectId=${contextId}`,\r\n    dataToSend\r\n  );\r\n  return data;\r\n};\r\n\r\nconst fetchQuestionBlockQuestions = async () => {\r\n  try {\r\n    const response = await axios.get(`/question-blocks`);\r\n    return response.data.questions || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching question block questions:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nconst updateQuestionPositions = async ({\r\n  contextType,\r\n  contextId,\r\n  questionPositions,\r\n}: {\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  questionPositions: { id: number; position: number }[];\r\n}) => {\r\n  // Only support position updates for projects currently\r\n  if (contextType !== \"project\") {\r\n    throw new Error(\r\n      \"Question position updates are only supported for projects\"\r\n    );\r\n  }\r\n\r\n  const url = `${getQuestionsEndPoint(\r\n    contextType\r\n  )}/positions?projectId=${contextId}`;\r\n  const payload = { questionPositions };\r\n\r\n  try {\r\n    const { data } = await axios.patch(url, payload);\r\n    return data;\r\n  } catch (error: any) {\r\n    console.error(\"Update failed - Full error:\", error);\r\n    console.error(\"Update failed - Error details:\", {\r\n      status: error.response?.status,\r\n      statusText: error.response?.statusText,\r\n      data: error.response?.data,\r\n      message: error.message,\r\n      config: {\r\n        url: error.config?.url,\r\n        method: error.config?.method,\r\n        data: error.config?.data,\r\n      },\r\n    });\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport {\r\n  fetchQuestions,\r\n  fetchTemplateQuestions,\r\n  addQuestion,\r\n  deleteQuestion,\r\n  duplicateQuestion,\r\n  updateQuestion,\r\n  fetchQuestionBlockQuestions,\r\n  updateQuestionPositions,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGA,MAAM,uBAAuB,CAAC;IAC5B,IAAI,gBAAgB,WAAW,OAAO;SACjC,IAAI,gBAAgB,YAAY,OAAO;SACvC,IAAI,gBAAgB,iBAAiB,OAAO;IACjD,MAAM,IAAI,MAAM;AAClB;AAEA,MAAM,iBAAiB,OAAO,EAAE,SAAS,EAAyB;IAChE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW;IAC1D,OAAO,KAAK,SAAS;AACvB;AAEA,MAAM,yBAAyB,OAAO,EACpC,UAAU,EAGX;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY;IACpE,OAAO,KAAK,SAAS;AACvB;AAEA,+BAA+B;AAC/B,iBAAiB;AACjB,eAAe;AACf,gBAAgB;AAChB,cAAc;AACd,OAAO;AACP,8BAA8B;AAC9B,uBAAuB;AACvB,kBAAkB;AAClB,qBAAqB;AACrB,2BAA2B;AAC3B,qBAAqB;AACrB,4BAA4B;AAC5B,yBAAyB;AACzB,0BAA0B;AAC1B,uBAAuB;AACvB,sBAAsB;AACtB,wCAAwC;AACxC,WAAW;AACX,OAAO;AACP,uBAAuB;AACvB,UAAU;AACV,8EAA8E;AAC9E,sEAAsE;AACtE,gBAAgB;AAChB,sCAAsC;AACtC,iDAAiD;AACjD,+DAA+D;AAE/D,6CAA6C;AAC7C,qBAAqB;AACrB,+BAA+B;AAC/B,QAAQ;AACR,iBAAiB;AACjB,KAAK;AAEL,+BAA+B;AAC/B,iBAAiB;AACjB,eAAe;AACf,gBAAgB;AAChB,cAAc;AACd,OAAO;AACP,8BAA8B;AAC9B,uBAAuB;AACvB,kBAAkB;AAClB,qBAAqB;AACrB,2BAA2B;AAC3B,qBAAqB;AACrB,4BAA4B;AAC5B,yBAAyB;AACzB,0BAA0B;AAC1B,uBAAuB;AACvB,sBAAsB;AACtB,wCAAwC;AACxC,WAAW;AACX,mBAAmB;AACnB,OAAO;AACP,uBAAuB;AACvB,UAAU;AACV,8EAA8E;AAC9E,sEAAsE;AACtE,gBAAgB;AAChB,sCAAsC;AACtC,iDAAiD;AACjD,+DAA+D;AAE/D,4DAA4D;AAC5D,qDAAqD;AAErD,mBAAmB;AACnB,yCAAyC;AACzC,uCAAuC;AAEvC,qCAAqC;AACrC,kDAAkD;AAClD,uEAAuE;AACvE,0DAA0D;AAE1D,6BAA6B;AAC7B,kDAAkD;AAClD,QAAQ;AAER,oCAAoC;AACpC,gEAAgE;AAChE,QAAQ;AAER,+DAA+D;AAE/D,+BAA+B;AAC/B,wDAAwD;AAExD,yDAAyD;AACzD,mBAAmB;AACnB,iDAAiD;AACjD,WAAW;AACX,UAAU;AAEV,mBAAmB;AACnB,aAAa;AACb,wCAAwC;AACxC,+CAA+C;AAC/C,iCAAiC;AACjC,2CAA2C;AAC3C,+BAA+B;AAC/B,6CAA6C;AAC7C,yCAAyC;AACzC,qDAAqD;AACrD,iCAAiC;AACjC,UAAU;AAEV,mBAAmB;AACnB,MAAM;AACN,KAAK;AAEL,MAAM,cAAc,OAAO,EACzB,WAAW,EACX,SAAS,EACT,UAAU,EACV,QAAQ,EAmBT;IACC,MAAM,MACJ,gBAAgB,kBACZ,GAAG,qBAAqB,cAAc,GACtC,GAAG,qBAAqB,aAAa,CAAC,EAAE,WAAW;IAEzD,2BAA2B;IAC3B,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,SAAS,EAAE;QAC9C,MAAM,IAAI,MAAM;IAClB;IAEA,4CAA4C;IAC5C,MAAM,eAAe;QAAC;QAAa;KAAa,CAAC,QAAQ,CACvD,WAAW,SAAS;IAEtB,MAAM,UAAU,WAAW,IAAI,YAAY;IAC3C,MAAM,aACJ,MAAM,OAAO,CAAC,WAAW,eAAe,KACxC,WAAW,eAAe,CAAC,MAAM,GAAG;IAEtC,yDAAyD;IACzD,IAAI,gBAAgB,CAAC,WAAW,CAAC,YAAY;QAC3C,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,SAAS;QACX,MAAM,WAAW,IAAI;QAErB,0BAA0B;QAC1B,SAAS,MAAM,CAAC,SAAS,WAAW,KAAK;QACzC,uDAAuD;QACvD,SAAS,MAAM,CAAC,cAAc,WAAW,UAAU,GAAG,SAAS;QAC/D,SAAS,MAAM,CAAC,aAAa,WAAW,SAAS;QACjD,IAAI,WAAW,IAAI,EAAE,SAAS,MAAM,CAAC,QAAQ,WAAW,IAAI;QAC5D,IAAI,WAAW,WAAW,EACxB,SAAS,MAAM,CAAC,eAAe,WAAW,WAAW;QACvD,2BAA2B;QAC3B,SAAS,MAAM,CAAC,YAAY,OAAO,YAAY;QAE/C,uCAAuC;QACvC,SAAS,MAAM,CAAC,QAAQ,WAAW,IAAI;QAEvC,kEAAkE;QAClE,kDAAkD;QAElD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,KAAK,UAAU;gBAC/C,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CACX,yBACA,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAEvC,MAAM,IAAI,MACR,CAAC,qCAAqC,EACpC,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAC9C;QAEN;IACF,OAAO;QACL,iCAAiC;QACjC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,KAAK;gBACrC,OAAO,WAAW,KAAK;gBACvB,YAAY,WAAW,UAAU;gBACjC,MAAM,WAAW,IAAI;gBACrB,aAAa,WAAW,WAAW;gBACnC,WAAW,WAAW,SAAS;gBAC/B,iBAAiB,WAAW,eAAe;gBAC3C,UAAU,YAAY;YACxB;YACA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CACX,sBACA,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAEvC,MAAM,IAAI,MACR,CAAC,wBAAwB,EACvB,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAC9C;QAEN;IACF;AACF;AACA,MAAM,iBAAiB,OAAO,EAC5B,WAAW,EACX,EAAE,EACF,SAAS,EAKV;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,MAAM,CACjC,GAAG,qBAAqB,aAAa,CAAC,EAAE,GAAG,WAAW,EAAE,WAAW;IAErE,OAAO;AACT;AAEA,MAAM,oBAAoB,OAAO,EAC/B,EAAE,EACF,WAAW,EACX,SAAS,EAKV;IACC,uEAAuE;IACvE,iEAAiE;IACjE,MAAM,cACJ,gBAAgB,kBACZ,CAAC,IACD,gBAAgB,YAChB;QAAE,WAAW;IAAU,IACvB;QAAE,YAAY;IAAU;IAE9B,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAC/B,GAAG,qBACD,aACA,WAAW,EAAE,GAAG,WAAW,EAAE,WAAW,EAC1C;IAGF,OAAO;AACT;AAEA,MAAM,iBAAiB,OAAO,EAC5B,EAAE,EACF,WAAW,EACX,UAAU,EACV,SAAS,EAkBV;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAChC,GAAG,qBAAqB,aAAa,CAAC,EAAE,GAAG,WAAW,EAAE,WAAW,EACnE;IAEF,OAAO;AACT;AAEA,MAAM,8BAA8B;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,gBAAgB,CAAC;QACnD,OAAO,SAAS,IAAI,CAAC,SAAS,IAAI,EAAE;IACtC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM;IACR;AACF;AAEA,MAAM,0BAA0B,OAAO,EACrC,WAAW,EACX,SAAS,EACT,iBAAiB,EAKlB;IACC,uDAAuD;IACvD,IAAI,gBAAgB,WAAW;QAC7B,MAAM,IAAI,MACR;IAEJ;IAEA,MAAM,MAAM,GAAG,qBACb,aACA,qBAAqB,EAAE,WAAW;IACpC,MAAM,UAAU;QAAE;IAAkB;IAEpC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,KAAK;QACxC,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,QAAQ,KAAK,CAAC,kCAAkC;YAC9C,QAAQ,MAAM,QAAQ,EAAE;YACxB,YAAY,MAAM,QAAQ,EAAE;YAC5B,MAAM,MAAM,QAAQ,EAAE;YACtB,SAAS,MAAM,OAAO;YACtB,QAAQ;gBACN,KAAK,MAAM,MAAM,EAAE;gBACnB,QAAQ,MAAM,MAAM,EAAE;gBACtB,MAAM,MAAM,MAAM,EAAE;YACtB;QACF;QACA,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/general/Spinner.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst Spinner = () => {\r\n  return (\r\n    <div className=\"w-full flex items-center justify-center\">\r\n      <div className=\"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16\"></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Spinner;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,UAAU;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;uCAEe", "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n/**\r\n * Format a date into a readable string format\r\n * @param date - Date object or string to format\r\n * @param format - Optional format type ('short', 'long', or 'full')\r\n * @returns Formatted date string\r\n */\r\nexport function formatDate(\r\n  date: Date | string,\r\n  format: \"short\" | \"long\" | \"full\" = \"short\"\r\n): string {\r\n  if (!date) return \"\";\r\n\r\n  try {\r\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\r\n\r\n    // Return empty string if invalid date\r\n    if (isNaN(dateObj.getTime())) return \"\";\r\n\r\n    switch (format) {\r\n      case \"short\":\r\n        return dateObj.toLocaleDateString(undefined, {\r\n          year: \"numeric\",\r\n          month: \"short\",\r\n          day: \"numeric\",\r\n        });\r\n\r\n      case \"long\":\r\n        return dateObj.toLocaleDateString(undefined, {\r\n          year: \"numeric\",\r\n          month: \"long\",\r\n          day: \"numeric\",\r\n          hour: \"2-digit\",\r\n          minute: \"2-digit\",\r\n        });\r\n\r\n      case \"full\":\r\n        return dateObj.toLocaleDateString(undefined, {\r\n          year: \"numeric\",\r\n          month: \"long\",\r\n          day: \"numeric\",\r\n          weekday: \"long\",\r\n          hour: \"2-digit\",\r\n          minute: \"2-digit\",\r\n          second: \"2-digit\",\r\n        });\r\n\r\n      default:\r\n        return dateObj.toLocaleDateString();\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error formatting date:\", error);\r\n    return String(date);\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAQO,SAAS,WACd,IAAmB,EACnB,SAAoC,OAAO;IAE3C,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;QAE5D,sCAAsC;QACtC,IAAI,MAAM,QAAQ,OAAO,KAAK,OAAO;QAErC,OAAQ;YACN,KAAK;gBACH,OAAO,QAAQ,kBAAkB,CAAC,WAAW;oBAC3C,MAAM;oBACN,OAAO;oBACP,KAAK;gBACP;YAEF,KAAK;gBACH,OAAO,QAAQ,kBAAkB,CAAC,WAAW;oBAC3C,MAAM;oBACN,OAAO;oBACP,KAAK;oBACL,MAAM;oBACN,QAAQ;gBACV;YAEF,KAAK;gBACH,OAAO,QAAQ,kBAAkB,CAAC,WAAW;oBAC3C,MAAM;oBACN,OAAO;oBACP,KAAK;oBACL,SAAS;oBACT,MAAM;oBACN,QAAQ;oBACR,QAAQ;gBACV;YAEF;gBACE,OAAO,QAAQ,kBAAkB;QACrC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,OAAO;IAChB;AACF", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label } "], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 556, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport interface TextareaProps\r\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\r\n  ({ className, ...props }, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          \"flex min-h-[80px] w-full rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-900 dark:placeholder:text-gray-500\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nTextarea.displayName = \"Textarea\";\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mUACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\r\nimport { CheckIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Checkbox({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\r\n  return (\r\n    <CheckboxPrimitive.Root\r\n      data-slot=\"checkbox\"\r\n      className={cn(\r\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <CheckboxPrimitive.Indicator\r\n        data-slot=\"checkbox-indicator\"\r\n        className=\"flex items-center justify-center text-current transition-none\"\r\n      >\r\n        <CheckIcon className=\"size-3.5\" />\r\n      </CheckboxPrimitive.Indicator>\r\n    </CheckboxPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Checkbox }\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 629, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\r\nimport { Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst RadioGroup = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Root\r\n      className={cn(\"grid gap-2\", className)}\r\n      {...props}\r\n      ref={ref}\r\n    />\r\n  )\r\n})\r\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\r\n\r\nconst RadioGroupItem = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Item\r\n      ref={ref}\r\n      className={cn(\r\n        \"aspect-square h-4 w-4 rounded-full border text-gray-900 shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus-visible:ring-gray-300\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\r\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\r\n      </RadioGroupPrimitive.Indicator>\r\n    </RadioGroupPrimitive.Item>\r\n  )\r\n})\r\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\r\n\r\nexport { RadioGroup, RadioGroupItem } "], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;QACT,KAAK;;;;;;AAGX;AACA,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yOACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,YAA6B;YAAC,WAAU;sBACvC,cAAA,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1B;AACA,eAAe,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 689, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/table.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 text-left align-middle font-medium neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gHACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 807, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-primary-500 focus-visible:ring-[1px]\",\r\n        \"focus-visible:outline-none\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mcACA,qFACA,8BACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 833, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/api/table.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\n\r\nexport interface TableColumn {\r\n  id: number;\r\n  columnName: string;\r\n  parentColumnId?: number;\r\n  childColumns?: TableColumn[];\r\n}\r\n\r\nexport interface TableRow {\r\n  id: number;\r\n  rowsName: string;\r\n}\r\n\r\nexport interface CellValue {\r\n  columnId: number;\r\n  rowsId: number;\r\n  value: string;\r\n  code?: string;\r\n}\r\n\r\nexport interface TableQuestion {\r\n  id: number;\r\n  label: string;\r\n  inputType: string;\r\n  tableColumns: TableColumn[];\r\n  tableRows: TableRow[];\r\n}\r\n\r\n// Fetch table structure (columns and rows)\r\nexport const fetchTableStructure = async (questionId: number) => {\r\n  try {\r\n    if (!questionId || isNaN(questionId)) {\r\n      console.error(\"Invalid questionId:\", questionId);\r\n      throw new Error(\"Invalid question ID provided\");\r\n    }\r\n\r\n    // First try the table-questions endpoint\r\n    try {\r\n      const response = await axios.get(`/table-questions/${questionId}`);\r\n\r\n      // Check if the response has the expected structure\r\n      if (response.data && response.data.data && response.data.data.question) {\r\n        return response.data.data.question;\r\n      } else if (response.data && response.data.data) {\r\n        return response.data.data;\r\n      } else if (response.data && response.data.success) {\r\n        return response.data;\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error from /table-questions/ endpoint:\", err);\r\n      // Continue to try the next endpoint\r\n    }\r\n\r\n    // If that fails, try the questions endpoint\r\n    try {\r\n      const response = await axios.get(`/questions/${questionId}`);\r\n\r\n      if (response.data && response.data.data) {\r\n        return response.data.data;\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error from /questions/ endpoint:\", err);\r\n      // Continue to try the next endpoint\r\n    }\r\n\r\n    // If that fails, try the tables endpoint as a last resort\r\n    try {\r\n      const response = await axios.get(`/tables/${questionId}`);\r\n\r\n      if (response.data && response.data.data && response.data.data.question) {\r\n        return response.data.data.question;\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error from /tables/ endpoint:\", err);\r\n    }\r\n\r\n    // If all endpoints fail, throw an error\r\n    console.error(\"All endpoints failed to return valid data\");\r\n    throw new Error(\"Failed to fetch table structure from any endpoint\");\r\n  } catch (error) {\r\n    console.error(\"Error fetching table structure:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Save cell values\r\nexport const saveCellValues = async (\r\n  questionId: number,\r\n  cellValues: CellValue[]\r\n) => {\r\n  try {\r\n    const { data } = await axios.post(`/table-questions/cells`, {\r\n      questionId,\r\n      cellValues,\r\n    });\r\n    return data.data;\r\n  } catch (error) {\r\n    console.error(\"Error saving cell values:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Create a new table\r\n// IMPORTANT: When specifying parentColumnId for new columns, you need to use position-based indices\r\n// (1-based) that reference the position of the parent column in the array.\r\n// For example, if column B is a child of column A, and column A is the first column in the array,\r\n// then column B's parentColumnId should be 1.\r\n// This is different from updateTable, which uses actual database IDs.\r\nexport const createTable = async (\r\n  label: string,\r\n  projectId: number,\r\n  columns: { columnName: string; parentColumnId?: number }[],\r\n  rows?: { rowsName: string }[]\r\n) => {\r\n  try {\r\n    // Validate inputs before sending to API\r\n    if (!label || !label.trim()) {\r\n      throw new Error(\"Table label is required\");\r\n    }\r\n\r\n    if (!projectId || isNaN(projectId)) {\r\n      throw new Error(\"Valid project ID is required\");\r\n    }\r\n\r\n    if (!columns || !Array.isArray(columns) || columns.length === 0) {\r\n      throw new Error(\"At least one column is required\");\r\n    }\r\n\r\n    // Rows are now optional - validate only if provided\r\n    if (rows && !Array.isArray(rows)) {\r\n      throw new Error(\"Rows must be an array if provided\");\r\n    }\r\n\r\n    // Ensure all columns have valid names\r\n    const invalidColumns = columns.filter(\r\n      (col) => !col.columnName || !col.columnName.trim()\r\n    );\r\n    if (invalidColumns.length > 0) {\r\n      throw new Error(\"All columns must have valid names\");\r\n    }\r\n\r\n    // Ensure all rows have valid names if rows are provided\r\n    if (rows) {\r\n      const invalidRows = rows.filter(\r\n        (row) => !row.rowsName || !row.rowsName.trim()\r\n      );\r\n      if (invalidRows.length > 0) {\r\n        throw new Error(\"All rows must have valid names\");\r\n      }\r\n    }\r\n\r\n    // The columns are already ordered correctly with parent-child relationships\r\n    // We just need to pass them through to the backend\r\n\r\n    // Create a clean version of the columns to send to the backend\r\n    const cleanedColumns: {\r\n      columnName: string;\r\n      parentColumnId?: number;\r\n    }[] = columns.map((col) => ({\r\n      columnName: col.columnName,\r\n      parentColumnId: col.parentColumnId,\r\n    }));\r\n\r\n    // Log the columns being sent to the backend\r\n\r\n    // Log the rearranged columns\r\n\r\n    // Use the table-questions endpoint which creates both a question and table structure\r\n    // Note: The axios instance is configured with baseURL that includes /api, so we don't need to add it here\r\n    const { data } = await axios.post(`/table-questions`, {\r\n      label,\r\n      projectId,\r\n      columns: cleanedColumns,\r\n      rows: rows || [],\r\n    });\r\n\r\n    if (!data || !data.success) {\r\n      throw new Error(data?.message || \"Failed to create table\");\r\n    }\r\n\r\n    return data.data;\r\n  } catch (error: any) {\r\n    console.error(\"Error creating table:\", error);\r\n\r\n    // Enhance error message with response details if available\r\n    if (error.response) {\r\n      console.error(\"Response status:\", error.response.status);\r\n      console.error(\"Response data:\", error.response.data);\r\n\r\n      // If we have a more specific error message from the server, use it\r\n      if (error.response.data && error.response.data.message) {\r\n        error.message = error.response.data.message;\r\n      }\r\n    }\r\n\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Delete a table\r\nexport const deleteTable = async (tableId: number) => {\r\n  try {\r\n    const { data } = await axios.delete(`/table-questions/${tableId}`);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting table:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Update an existing table\r\n// IMPORTANT: When specifying parentColumnId for existing columns, use the actual database ID of the parent column.\r\n// For new columns (without an ID), use the position (1-based index) of the parent column in the array.\r\n// For example:\r\n// - If column B is a child of existing column A with ID 123, then column B's parentColumnId should be 123.\r\n// - If column B is a child of new column A at position 1 in the array, then column B's parentColumnId should be 1.\r\nexport const updateTable = async (\r\n  tableId: number,\r\n  label: string,\r\n  columns: { id?: number; columnName: string; parentColumnId?: number }[],\r\n  rows?: { id?: number; rowsName: string }[]\r\n) => {\r\n  try {\r\n    // Validate inputs before sending to API\r\n    if (!label || !label.trim()) {\r\n      throw new Error(\"Table label is required\");\r\n    }\r\n\r\n    if (!tableId || isNaN(tableId)) {\r\n      throw new Error(\"Valid table ID is required\");\r\n    }\r\n\r\n    if (!columns || !Array.isArray(columns) || columns.length === 0) {\r\n      throw new Error(\"At least one column is required\");\r\n    }\r\n\r\n    // Rows are now optional - validate only if provided\r\n    if (rows && !Array.isArray(rows)) {\r\n      throw new Error(\"Rows must be an array if provided\");\r\n    }\r\n\r\n    // Ensure all columns have valid names\r\n    const invalidColumns = columns.filter(\r\n      (col) => !col.columnName || !col.columnName.trim()\r\n    );\r\n    if (invalidColumns.length > 0) {\r\n      throw new Error(\"All columns must have valid names\");\r\n    }\r\n\r\n    // Ensure all rows have valid names if rows are provided\r\n    if (rows) {\r\n      const invalidRows = rows.filter(\r\n        (row) => !row.rowsName || !row.rowsName.trim()\r\n      );\r\n      if (invalidRows.length > 0) {\r\n        throw new Error(\"All rows must have valid names\");\r\n      }\r\n    }\r\n\r\n    // Validate parent-child relationships\r\n    // Check for circular references or invalid parent IDs\r\n    const columnIdMap = new Map();\r\n    const columnPositionMap = new Map();\r\n\r\n    // Map columns by ID and position\r\n    columns.forEach((col, index) => {\r\n      if (col.id) {\r\n        columnIdMap.set(col.id, col);\r\n      }\r\n      // Store 1-based position\r\n      columnPositionMap.set(index + 1, col);\r\n    });\r\n\r\n    // Check each column with a parent\r\n    for (const col of columns) {\r\n      if (col.parentColumnId) {\r\n        // Ensure parentColumnId is a positive number\r\n        if (col.parentColumnId <= 0) {\r\n          throw new Error(\r\n            `Invalid parent column ID: ${col.parentColumnId}. Must be a positive number.`\r\n          );\r\n        }\r\n\r\n        // Try to find parent by ID first\r\n        let parentCol = columns.find((c) => c.id === col.parentColumnId);\r\n\r\n        // If not found by ID, try to find by position (for new columns)\r\n        if (!parentCol && col.parentColumnId <= columns.length) {\r\n          parentCol = columnPositionMap.get(col.parentColumnId);\r\n        }\r\n\r\n        // If we still can't find the parent, it's an error\r\n        if (!parentCol) {\r\n          throw new Error(\r\n            `Parent column with ID/position ${col.parentColumnId} not found in the columns array.`\r\n          );\r\n        }\r\n\r\n        // Check for circular references\r\n        // If this column has a parent, and that parent also has a parent,\r\n        // it would create a 3rd level, which we don't support\r\n        if (parentCol.parentColumnId) {\r\n          throw new Error(\r\n            \"Cannot create more than 2 levels of nested columns (parent → child → grandchild)\"\r\n          );\r\n        }\r\n      }\r\n    }\r\n\r\n    // The columns are already ordered correctly with parent-child relationships\r\n    // We just need to pass them through to the backend\r\n\r\n    // Create a clean version of the columns to send to the backend\r\n    const cleanedColumns: {\r\n      id?: number;\r\n      columnName: string;\r\n      parentColumnId?: number;\r\n    }[] = columns.map((col) => {\r\n      const cleanCol: {\r\n        id?: number;\r\n        columnName: string;\r\n        parentColumnId?: number;\r\n      } = {\r\n        columnName: col.columnName.trim(),\r\n      };\r\n\r\n      if (col.id) {\r\n        cleanCol.id = col.id;\r\n      }\r\n\r\n      if (col.parentColumnId !== undefined) {\r\n        cleanCol.parentColumnId = col.parentColumnId;\r\n      }\r\n\r\n      return cleanCol;\r\n    });\r\n\r\n    // Log the columns being sent to the backend\r\n\r\n    // Use the table-questions endpoint to update the table\r\n    try {\r\n      const { data } = await axios.patch(`/table-questions/${tableId}`, {\r\n        label: label.trim(),\r\n        columns: cleanedColumns,\r\n        rows: rows\r\n          ? rows.map((row) => ({\r\n              ...row,\r\n              rowsName: row.rowsName.trim(),\r\n            }))\r\n          : [],\r\n      });\r\n\r\n      if (!data || !data.success) {\r\n        throw new Error(data?.message || \"Failed to update table\");\r\n      }\r\n\r\n      return data.data;\r\n    } catch (apiError: any) {\r\n      console.error(\"API error updating table:\", apiError);\r\n\r\n      // Enhance error message with response details if available\r\n      if (apiError.response) {\r\n        console.error(\"Response status:\", apiError.response.status);\r\n        console.error(\"Response data:\", apiError.response.data);\r\n\r\n        // If we have a more specific error message from the server, use it\r\n        if (apiError.response.data && apiError.response.data.message) {\r\n          throw new Error(apiError.response.data.message);\r\n        }\r\n      }\r\n\r\n      // If we don't have a specific error message, throw the original error\r\n      throw apiError;\r\n    }\r\n  } catch (error: any) {\r\n    console.error(\"Error updating table:\", error);\r\n\r\n    // Rethrow the error with a clear message\r\n    if (error.message) {\r\n      throw new Error(`Failed to update table: ${error.message}`);\r\n    } else {\r\n      throw new Error(\"Failed to update table due to an unknown error\");\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AA8BO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,IAAI,CAAC,cAAc,MAAM,aAAa;YACpC,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,IAAI,MAAM;QAClB;QAEA,yCAAyC;QACzC,IAAI;YACF,MAAM,WAAW,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,YAAY;YAEjE,mDAAmD;YACnD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACtE,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;YACpC,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC9C,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACjD,OAAO,SAAS,IAAI;YACtB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0CAA0C;QACxD,oCAAoC;QACtC;QAEA,4CAA4C;QAC5C,IAAI;YACF,MAAM,WAAW,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,YAAY;YAE3D,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBACvC,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oCAAoC;QAClD,oCAAoC;QACtC;QAEA,0DAA0D;QAC1D,IAAI;YACF,MAAM,WAAW,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY;YAExD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACtE,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;YACpC;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iCAAiC;QACjD;QAEA,wCAAwC;QACxC,QAAQ,KAAK,CAAC;QACd,MAAM,IAAI,MAAM;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,iBAAiB,OAC5B,YACA;IAEA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,sBAAsB,CAAC,EAAE;YAC1D;YACA;QACF;QACA,OAAO,KAAK,IAAI;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAQO,MAAM,cAAc,OACzB,OACA,WACA,SACA;IAEA,IAAI;QACF,wCAAwC;QACxC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,aAAa,MAAM,YAAY;YAClC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,YAAY,QAAQ,MAAM,KAAK,GAAG;YAC/D,MAAM,IAAI,MAAM;QAClB;QAEA,oDAAoD;QACpD,IAAI,QAAQ,CAAC,MAAM,OAAO,CAAC,OAAO;YAChC,MAAM,IAAI,MAAM;QAClB;QAEA,sCAAsC;QACtC,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,MAAQ,CAAC,IAAI,UAAU,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI;QAElD,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,wDAAwD;QACxD,IAAI,MAAM;YACR,MAAM,cAAc,KAAK,MAAM,CAC7B,CAAC,MAAQ,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI;YAE9C,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,4EAA4E;QAC5E,mDAAmD;QAEnD,+DAA+D;QAC/D,MAAM,iBAGA,QAAQ,GAAG,CAAC,CAAC,MAAQ,CAAC;gBAC1B,YAAY,IAAI,UAAU;gBAC1B,gBAAgB,IAAI,cAAc;YACpC,CAAC;QAED,4CAA4C;QAE5C,6BAA6B;QAE7B,qFAAqF;QACrF,0GAA0G;QAC1G,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,EAAE;YACpD;YACA;YACA,SAAS;YACT,MAAM,QAAQ,EAAE;QAClB;QAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE;YAC1B,MAAM,IAAI,MAAM,MAAM,WAAW;QACnC;QAEA,OAAO,KAAK,IAAI;IAClB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,2DAA2D;QAC3D,IAAI,MAAM,QAAQ,EAAE;YAClB,QAAQ,KAAK,CAAC,oBAAoB,MAAM,QAAQ,CAAC,MAAM;YACvD,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;YAEnD,mEAAmE;YACnE,IAAI,MAAM,QAAQ,CAAC,IAAI,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;gBACtD,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C;QACF;QAEA,MAAM;IACR;AACF;AAGO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,SAAS;QACjE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAQO,MAAM,cAAc,OACzB,SACA,OACA,SACA;IAEA,IAAI;QACF,wCAAwC;QACxC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,WAAW,MAAM,UAAU;YAC9B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,YAAY,QAAQ,MAAM,KAAK,GAAG;YAC/D,MAAM,IAAI,MAAM;QAClB;QAEA,oDAAoD;QACpD,IAAI,QAAQ,CAAC,MAAM,OAAO,CAAC,OAAO;YAChC,MAAM,IAAI,MAAM;QAClB;QAEA,sCAAsC;QACtC,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,MAAQ,CAAC,IAAI,UAAU,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI;QAElD,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,wDAAwD;QACxD,IAAI,MAAM;YACR,MAAM,cAAc,KAAK,MAAM,CAC7B,CAAC,MAAQ,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI;YAE9C,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,sCAAsC;QACtC,sDAAsD;QACtD,MAAM,cAAc,IAAI;QACxB,MAAM,oBAAoB,IAAI;QAE9B,iCAAiC;QACjC,QAAQ,OAAO,CAAC,CAAC,KAAK;YACpB,IAAI,IAAI,EAAE,EAAE;gBACV,YAAY,GAAG,CAAC,IAAI,EAAE,EAAE;YAC1B;YACA,yBAAyB;YACzB,kBAAkB,GAAG,CAAC,QAAQ,GAAG;QACnC;QAEA,kCAAkC;QAClC,KAAK,MAAM,OAAO,QAAS;YACzB,IAAI,IAAI,cAAc,EAAE;gBACtB,6CAA6C;gBAC7C,IAAI,IAAI,cAAc,IAAI,GAAG;oBAC3B,MAAM,IAAI,MACR,CAAC,0BAA0B,EAAE,IAAI,cAAc,CAAC,4BAA4B,CAAC;gBAEjF;gBAEA,iCAAiC;gBACjC,IAAI,YAAY,QAAQ,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,IAAI,cAAc;gBAE/D,gEAAgE;gBAChE,IAAI,CAAC,aAAa,IAAI,cAAc,IAAI,QAAQ,MAAM,EAAE;oBACtD,YAAY,kBAAkB,GAAG,CAAC,IAAI,cAAc;gBACtD;gBAEA,mDAAmD;gBACnD,IAAI,CAAC,WAAW;oBACd,MAAM,IAAI,MACR,CAAC,+BAA+B,EAAE,IAAI,cAAc,CAAC,gCAAgC,CAAC;gBAE1F;gBAEA,gCAAgC;gBAChC,kEAAkE;gBAClE,sDAAsD;gBACtD,IAAI,UAAU,cAAc,EAAE;oBAC5B,MAAM,IAAI,MACR;gBAEJ;YACF;QACF;QAEA,4EAA4E;QAC5E,mDAAmD;QAEnD,+DAA+D;QAC/D,MAAM,iBAIA,QAAQ,GAAG,CAAC,CAAC;YACjB,MAAM,WAIF;gBACF,YAAY,IAAI,UAAU,CAAC,IAAI;YACjC;YAEA,IAAI,IAAI,EAAE,EAAE;gBACV,SAAS,EAAE,GAAG,IAAI,EAAE;YACtB;YAEA,IAAI,IAAI,cAAc,KAAK,WAAW;gBACpC,SAAS,cAAc,GAAG,IAAI,cAAc;YAC9C;YAEA,OAAO;QACT;QAEA,4CAA4C;QAE5C,uDAAuD;QACvD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,iBAAiB,EAAE,SAAS,EAAE;gBAChE,OAAO,MAAM,IAAI;gBACjB,SAAS;gBACT,MAAM,OACF,KAAK,GAAG,CAAC,CAAC,MAAQ,CAAC;wBACjB,GAAG,GAAG;wBACN,UAAU,IAAI,QAAQ,CAAC,IAAI;oBAC7B,CAAC,KACD,EAAE;YACR;YAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE;gBAC1B,MAAM,IAAI,MAAM,MAAM,WAAW;YACnC;YAEA,OAAO,KAAK,IAAI;QAClB,EAAE,OAAO,UAAe;YACtB,QAAQ,KAAK,CAAC,6BAA6B;YAE3C,2DAA2D;YAC3D,IAAI,SAAS,QAAQ,EAAE;gBACrB,QAAQ,KAAK,CAAC,oBAAoB,SAAS,QAAQ,CAAC,MAAM;gBAC1D,QAAQ,KAAK,CAAC,kBAAkB,SAAS,QAAQ,CAAC,IAAI;gBAEtD,mEAAmE;gBACnE,IAAI,SAAS,QAAQ,CAAC,IAAI,IAAI,SAAS,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;oBAC5D,MAAM,IAAI,MAAM,SAAS,QAAQ,CAAC,IAAI,CAAC,OAAO;gBAChD;YACF;YAEA,sEAAsE;YACtE,MAAM;QACR;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,yCAAyC;QACzC,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,MAAM,OAAO,EAAE;QAC5D,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 1099, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/form-inputs/TableInput.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow as UITableRow,\r\n} from \"@/components/ui/table\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  fetchTableStructure,\r\n  TableColumn,\r\n  TableRow as TableRowType,\r\n  CellValue,\r\n} from \"../../lib/api/table\";\r\n\r\ninterface TableInputProps {\r\n  questionId: number;\r\n  value: string | CellValue[];\r\n  onChange: (value: CellValue[]) => void;\r\n  required?: boolean;\r\n  tableLabel?: string;\r\n}\r\n\r\nexport function TableInput({\r\n  questionId,\r\n  value,\r\n  onChange,\r\n  required = false,\r\n  tableLabel,\r\n}: TableInputProps) {\r\n  // All state hooks at the top of the component\r\n  const [columns, setColumns] = useState<TableColumn[]>([]);\r\n  const [rows, setRows] = useState<TableRowType[]>([]);\r\n  const [cellValues, setCellValues] = useState<Record<string, string>>({});\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [tableInfo, setTableInfo] = useState<{ label?: string }>({});\r\n\r\n  // Process columns to create a flat structure with parent-child relationships\r\n  const processColumns = (tableData: any) => {\r\n    if (!tableData || !tableData.tableColumns) return [];\r\n\r\n    const flattenedColumns: TableColumn[] = [];\r\n    const parentColumns = tableData.tableColumns.filter(\r\n      (col: TableColumn) =>\r\n        col.parentColumnId === null || col.parentColumnId === undefined\r\n    );\r\n\r\n    // Process each parent column and its children\r\n    parentColumns.forEach((parentCol: TableColumn) => {\r\n      // Add the parent column\r\n      flattenedColumns.push(parentCol);\r\n\r\n      // Add child columns if they exist\r\n      if (parentCol.childColumns && parentCol.childColumns.length > 0) {\r\n        parentCol.childColumns.forEach((childCol: any) => {\r\n          flattenedColumns.push({\r\n            id: childCol.id,\r\n            columnName: childCol.columnName,\r\n            parentColumnId: childCol.parentColumnId,\r\n          });\r\n        });\r\n      }\r\n    });\r\n\r\n    return flattenedColumns;\r\n  };\r\n\r\n  // IMPORTANT: All hooks must be called unconditionally and in the same order every render\r\n  // Group columns by parent-child relationships - always called, never conditional\r\n  const groupedColumns = React.useMemo(() => {\r\n    // Default empty values for when columns are not loaded yet\r\n    if (columns.length === 0) {\r\n      return {\r\n        parentColumns: [],\r\n        columnMap: new Map<number, TableColumn[]>(),\r\n        hasChildColumns: false,\r\n      };\r\n    }\r\n\r\n    // Get all parent columns (those without a parentColumnId)\r\n    const parentColumns = columns.filter(\r\n      (col) => col.parentColumnId === undefined || col.parentColumnId === null\r\n    );\r\n\r\n    // Create a map of parent columns to their child columns\r\n    const columnMap = new Map<number, TableColumn[]>();\r\n\r\n    parentColumns.forEach((parentCol) => {\r\n      // Find all child columns for this parent\r\n      const childColumns = columns.filter(\r\n        (col) => col.parentColumnId === parentCol.id\r\n      );\r\n      columnMap.set(parentCol.id, childColumns);\r\n    });\r\n\r\n    // Check if any parent has child columns\r\n    const hasChildColumns = parentColumns.some(\r\n      (p) => (columnMap.get(p.id) || []).length > 0\r\n    );\r\n\r\n    return { parentColumns, columnMap, hasChildColumns };\r\n  }, [columns]);\r\n\r\n  // Fetch table structure (columns and rows) on component mount or when questionId changes\r\n  useEffect(() => {\r\n    const loadTableStructure = async () => {\r\n      try {\r\n        setLoading(true);\r\n\r\n        const tableData = await fetchTableStructure(questionId);\r\n\r\n        if (tableData) {\r\n          // Check if tableColumns and tableRows exist\r\n          if (!tableData.tableColumns || !tableData.tableRows) {\r\n            console.error(\r\n              \"Missing tableColumns or tableRows in response:\",\r\n              tableData\r\n            );\r\n          }\r\n\r\n          // Process columns to handle parent-child relationships\r\n          const processedColumns = processColumns(tableData);\r\n          setColumns(processedColumns);\r\n          setRows(tableData.tableRows || []);\r\n\r\n          // Store the table label if available\r\n          if (tableData.label) {\r\n            setTableInfo({ label: tableData.label });\r\n          }\r\n\r\n         \r\n        } else {\r\n          console.error(\"No table data returned\");\r\n          setError(\"Failed to load table structure\");\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error fetching table structure:\", err);\r\n        setError(\"Failed to load table structure\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadTableStructure();\r\n  }, [questionId]); // Only reload when questionId changes, not when value changes\r\n\r\n  // Handle value changes separately without reloading the table structure\r\n  useEffect(() => {\r\n    // Don't process if we're still loading the table structure\r\n    if (loading) return;\r\n\r\n    // Initialize cell values from existing data if available\r\n    const initialCellValues: Record<string, string> = {};\r\n\r\n    // If value is a string, try to parse it as JSON\r\n    let cellData: CellValue[] = [];\r\n    if (typeof value === \"string\") {\r\n      // Only attempt to parse if the string is not empty\r\n      if (value && value.trim() !== \"\") {\r\n        try {\r\n          cellData = JSON.parse(value);\r\n        } catch (e) {\r\n          console.error(\"Error parsing cell data:\", e);\r\n          cellData = [];\r\n        }\r\n      } else {\r\n        console.error(\"Empty string value, using empty array\");\r\n      }\r\n    } else if (Array.isArray(value)) {\r\n      cellData = value;\r\n    }\r\n\r\n    // Convert cell data to a map for easier access\r\n    cellData.forEach((cell) => {\r\n      initialCellValues[`${cell.columnId}_${cell.rowsId}`] = cell.value;\r\n    });\r\n\r\n    // Check if the value indicates a form reset (empty array, empty string, or undefined)\r\n    const isFormReset =\r\n      !value ||\r\n      (typeof value === \"string\" && value.trim() === \"\") ||\r\n      (Array.isArray(value) && value.length === 0);\r\n\r\n    if (isFormReset) {\r\n      // Clear all cell values when form is reset\r\n      setCellValues({});\r\n    } else if (Object.keys(initialCellValues).length > 0) {\r\n      // Only update cell values if we have new data and we're not in the middle of editing\r\n      setCellValues((prev) => {\r\n        // Merge with existing values to avoid losing user input\r\n        return { ...initialCellValues, ...prev };\r\n      });\r\n    }\r\n  }, [value, loading]);\r\n\r\n  // Handle cell value change\r\n  const handleCellChange = (\r\n    columnId: number,\r\n    rowId: number,\r\n    newValue: string\r\n  ) => {\r\n    const cellKey = `${columnId}_${rowId}`;\r\n\r\n    // Update the cell values state\r\n    setCellValues((prev) => ({\r\n      ...prev,\r\n      [cellKey]: newValue,\r\n    }));\r\n\r\n    // Use a setTimeout to ensure we're working with the latest state\r\n    // This prevents the race condition where the state update hasn't completed yet\r\n    setTimeout(() => {\r\n      // Get the current state of cellValues after the update\r\n      const currentCellValues = { ...cellValues, [cellKey]: newValue };\r\n\r\n      // Convert the updated cell values to the format expected by the onChange handler\r\n      const updatedCellValues: CellValue[] = [];\r\n\r\n      // Convert all cell values to the expected format\r\n      Object.entries(currentCellValues).forEach(([key, value]) => {\r\n        if (value.trim() !== \"\") {\r\n          const [colId, rowId] = key.split(\"_\").map(Number);\r\n          updatedCellValues.push({\r\n            columnId: colId,\r\n            rowsId: rowId,\r\n            value,\r\n          });\r\n        }\r\n      });\r\n\r\n      // Call the onChange handler with all cell values\r\n      onChange(updatedCellValues);\r\n    }, 0);\r\n  };\r\n\r\n  // Calculate this once, outside of any conditional rendering\r\n  // Only show error when there are no columns - having no rows is valid\r\n  const hasNoColumns = columns.length === 0;\r\n\r\n  // Render the hierarchical table\r\n  // Use a single return statement with conditional rendering inside\r\n  return (\r\n    <div className=\"overflow-x-auto\">\r\n      {loading ? (\r\n        <div className=\"py-4 text-center\">Loading table...</div>\r\n      ) : error ? (\r\n        <div className=\"py-4 text-center text-red-500\">{error}</div>\r\n      ) : hasNoColumns ? (\r\n        <div className=\"py-4 text-center text-amber-600\">\r\n          This table has no columns defined. Please configure the table question\r\n          first.\r\n        </div>\r\n      ) : (\r\n        <Table className=\"border-collapse\">\r\n          <TableHeader>\r\n            {/* First row: Parent column headers starting from leftmost position */}\r\n            <UITableRow>\r\n              {groupedColumns.parentColumns.map((parentCol) => {\r\n                const childColumns =\r\n                  groupedColumns.columnMap.get(parentCol.id) || [];\r\n                // If this parent has children, it spans multiple columns\r\n                const colSpan = childColumns.length || 1;\r\n\r\n                return (\r\n                  <TableHead\r\n                    key={parentCol.id}\r\n                    colSpan={colSpan}\r\n                    className=\"text-center border bg-blue-50 font-medium\"\r\n                  >\r\n                    {parentCol.columnName}\r\n                  </TableHead>\r\n                );\r\n              })}\r\n            </UITableRow>\r\n\r\n            {/* Second row: Child column headers (only if there are child columns) */}\r\n            {groupedColumns.hasChildColumns && (\r\n              <UITableRow>\r\n                {groupedColumns.parentColumns.map((parentCol) => {\r\n                  const childColumns =\r\n                    groupedColumns.columnMap.get(parentCol.id) || [];\r\n\r\n                  // If this parent has no children, render an empty cell to maintain alignment\r\n                  if (childColumns.length === 0) {\r\n                    return (\r\n                      <TableHead\r\n                        key={`empty-${parentCol.id}`}\r\n                        className=\"border bg-blue-50/50 text-sm\"\r\n                      >\r\n                        {/* Empty cell to maintain column alignment */}\r\n                      </TableHead>\r\n                    );\r\n                  }\r\n\r\n                  // Otherwise, render each child column\r\n                  return childColumns.map((childCol) => (\r\n                    <TableHead\r\n                      key={childCol.id}\r\n                      className=\"border bg-blue-50/50 text-sm\"\r\n                    >\r\n                      {childCol.columnName}\r\n                    </TableHead>\r\n                  ));\r\n                })}\r\n              </UITableRow>\r\n            )}\r\n          </TableHeader>\r\n\r\n          <TableBody>\r\n            {rows.length > 0 ? (\r\n              rows.map((row, rowIndex) => (\r\n                <UITableRow\r\n                  key={row.id}\r\n                  className={rowIndex % 2 === 0 ? \"bg-white\" : \"bg-gray-50\"}\r\n                >\r\n                  {/* Render cells for each parent column starting from leftmost position */}\r\n                  {groupedColumns.parentColumns.map((parentCol) => {\r\n                    const childColumns =\r\n                      groupedColumns.columnMap.get(parentCol.id) || [];\r\n\r\n                    // If this parent has no children, render a single cell\r\n                    if (childColumns.length === 0) {\r\n                      return (\r\n                        <TableCell\r\n                          key={`cell-${parentCol.id}-${row.id}`}\r\n                          className=\"border p-1\"\r\n                        >\r\n                          <Input\r\n                            value={\r\n                              cellValues[`${parentCol.id}_${row.id}`] || \"\"\r\n                            }\r\n                            onChange={(e) =>\r\n                              handleCellChange(\r\n                                parentCol.id,\r\n                                row.id,\r\n                                e.target.value\r\n                              )\r\n                            }\r\n                            className=\"w-full\"\r\n                            required={required}\r\n                            placeholder=\"Enter value\"\r\n                          />\r\n                        </TableCell>\r\n                      );\r\n                    }\r\n\r\n                    // Otherwise, render cells for each child column\r\n                    return childColumns.map((childCol) => (\r\n                      <TableCell\r\n                        key={`cell-${childCol.id}-${row.id}`}\r\n                        className=\"border p-1\"\r\n                      >\r\n                        <Input\r\n                          value={cellValues[`${childCol.id}_${row.id}`] || \"\"}\r\n                          onChange={(e) =>\r\n                            handleCellChange(\r\n                              childCol.id,\r\n                              row.id,\r\n                              e.target.value\r\n                            )\r\n                          }\r\n                          className=\"w-full\"\r\n                          required={required}\r\n                          placeholder=\"Enter value\"\r\n                        />\r\n                      </TableCell>\r\n                    ));\r\n                  })}\r\n                </UITableRow>\r\n              ))\r\n            ) : (\r\n              // When no rows exist, show a single row with input fields under columns\r\n              <UITableRow>\r\n                {/* Render input cells for each parent column starting from leftmost position */}\r\n                {groupedColumns.parentColumns.map((parentCol) => {\r\n                  const childColumns =\r\n                    groupedColumns.columnMap.get(parentCol.id) || [];\r\n\r\n                  // If this parent has no children, render a single cell\r\n                  if (childColumns.length === 0) {\r\n                    return (\r\n                      <TableCell\r\n                        key={`cell-${parentCol.id}-no-row`}\r\n                        className=\"border p-1\"\r\n                      >\r\n                        <Input\r\n                          value={cellValues[`${parentCol.id}_no_row`] || \"\"}\r\n                          onChange={(e) =>\r\n                            handleCellChange(\r\n                              parentCol.id,\r\n                              \"no_row\" as any,\r\n                              e.target.value\r\n                            )\r\n                          }\r\n                          className=\"w-full\"\r\n                          required={required}\r\n                          placeholder=\"Enter value\"\r\n                        />\r\n                      </TableCell>\r\n                    );\r\n                  }\r\n\r\n                  // Otherwise, render cells for each child column\r\n                  return childColumns.map((childCol) => (\r\n                    <TableCell\r\n                      key={`cell-${childCol.id}-no-row`}\r\n                      className=\"border p-1\"\r\n                    >\r\n                      <Input\r\n                        value={cellValues[`${childCol.id}_no_row`] || \"\"}\r\n                        onChange={(e) =>\r\n                          handleCellChange(\r\n                            childCol.id,\r\n                            \"no_row\" as any,\r\n                            e.target.value\r\n                          )\r\n                        }\r\n                        className=\"w-full\"\r\n                        required={required}\r\n                        placeholder=\"Enter value\"\r\n                      />\r\n                    </TableCell>\r\n                  ));\r\n                })}\r\n              </UITableRow>\r\n            )}\r\n          </TableBody>\r\n        </Table>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAZA;;;;;;AA2BO,SAAS,WAAW,EACzB,UAAU,EACV,KAAK,EACL,QAAQ,EACR,WAAW,KAAK,EAChB,UAAU,EACM;IAChB,8CAA8C;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACxD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,CAAC;IAEhE,6EAA6E;IAC7E,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,aAAa,CAAC,UAAU,YAAY,EAAE,OAAO,EAAE;QAEpD,MAAM,mBAAkC,EAAE;QAC1C,MAAM,gBAAgB,UAAU,YAAY,CAAC,MAAM,CACjD,CAAC,MACC,IAAI,cAAc,KAAK,QAAQ,IAAI,cAAc,KAAK;QAG1D,8CAA8C;QAC9C,cAAc,OAAO,CAAC,CAAC;YACrB,wBAAwB;YACxB,iBAAiB,IAAI,CAAC;YAEtB,kCAAkC;YAClC,IAAI,UAAU,YAAY,IAAI,UAAU,YAAY,CAAC,MAAM,GAAG,GAAG;gBAC/D,UAAU,YAAY,CAAC,OAAO,CAAC,CAAC;oBAC9B,iBAAiB,IAAI,CAAC;wBACpB,IAAI,SAAS,EAAE;wBACf,YAAY,SAAS,UAAU;wBAC/B,gBAAgB,SAAS,cAAc;oBACzC;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,yFAAyF;IACzF,iFAAiF;IACjF,MAAM,iBAAiB,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACnC,2DAA2D;QAC3D,IAAI,QAAQ,MAAM,KAAK,GAAG;YACxB,OAAO;gBACL,eAAe,EAAE;gBACjB,WAAW,IAAI;gBACf,iBAAiB;YACnB;QACF;QAEA,0DAA0D;QAC1D,MAAM,gBAAgB,QAAQ,MAAM,CAClC,CAAC,MAAQ,IAAI,cAAc,KAAK,aAAa,IAAI,cAAc,KAAK;QAGtE,wDAAwD;QACxD,MAAM,YAAY,IAAI;QAEtB,cAAc,OAAO,CAAC,CAAC;YACrB,yCAAyC;YACzC,MAAM,eAAe,QAAQ,MAAM,CACjC,CAAC,MAAQ,IAAI,cAAc,KAAK,UAAU,EAAE;YAE9C,UAAU,GAAG,CAAC,UAAU,EAAE,EAAE;QAC9B;QAEA,wCAAwC;QACxC,MAAM,kBAAkB,cAAc,IAAI,CACxC,CAAC,IAAM,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM,GAAG;QAG9C,OAAO;YAAE;YAAe;YAAW;QAAgB;IACrD,GAAG;QAAC;KAAQ;IAEZ,yFAAyF;IACzF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB;YACzB,IAAI;gBACF,WAAW;gBAEX,MAAM,YAAY,MAAM,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE;gBAE5C,IAAI,WAAW;oBACb,4CAA4C;oBAC5C,IAAI,CAAC,UAAU,YAAY,IAAI,CAAC,UAAU,SAAS,EAAE;wBACnD,QAAQ,KAAK,CACX,kDACA;oBAEJ;oBAEA,uDAAuD;oBACvD,MAAM,mBAAmB,eAAe;oBACxC,WAAW;oBACX,QAAQ,UAAU,SAAS,IAAI,EAAE;oBAEjC,qCAAqC;oBACrC,IAAI,UAAU,KAAK,EAAE;wBACnB,aAAa;4BAAE,OAAO,UAAU,KAAK;wBAAC;oBACxC;gBAGF,OAAO;oBACL,QAAQ,KAAK,CAAC;oBACd,SAAS;gBACX;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;KAAW,GAAG,8DAA8D;IAEhF,wEAAwE;IACxE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,2DAA2D;QAC3D,IAAI,SAAS;QAEb,yDAAyD;QACzD,MAAM,oBAA4C,CAAC;QAEnD,gDAAgD;QAChD,IAAI,WAAwB,EAAE;QAC9B,IAAI,OAAO,UAAU,UAAU;YAC7B,mDAAmD;YACnD,IAAI,SAAS,MAAM,IAAI,OAAO,IAAI;gBAChC,IAAI;oBACF,WAAW,KAAK,KAAK,CAAC;gBACxB,EAAE,OAAO,GAAG;oBACV,QAAQ,KAAK,CAAC,4BAA4B;oBAC1C,WAAW,EAAE;gBACf;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;YAC/B,WAAW;QACb;QAEA,+CAA+C;QAC/C,SAAS,OAAO,CAAC,CAAC;YAChB,iBAAiB,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE,CAAC,GAAG,KAAK,KAAK;QACnE;QAEA,sFAAsF;QACtF,MAAM,cACJ,CAAC,SACA,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,MAC9C,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK;QAE5C,IAAI,aAAa;YACf,2CAA2C;YAC3C,cAAc,CAAC;QACjB,OAAO,IAAI,OAAO,IAAI,CAAC,mBAAmB,MAAM,GAAG,GAAG;YACpD,qFAAqF;YACrF,cAAc,CAAC;gBACb,wDAAwD;gBACxD,OAAO;oBAAE,GAAG,iBAAiB;oBAAE,GAAG,IAAI;gBAAC;YACzC;QACF;IACF,GAAG;QAAC;QAAO;KAAQ;IAEnB,2BAA2B;IAC3B,MAAM,mBAAmB,CACvB,UACA,OACA;QAEA,MAAM,UAAU,GAAG,SAAS,CAAC,EAAE,OAAO;QAEtC,+BAA+B;QAC/B,cAAc,CAAC,OAAS,CAAC;gBACvB,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE;YACb,CAAC;QAED,iEAAiE;QACjE,+EAA+E;QAC/E,WAAW;YACT,uDAAuD;YACvD,MAAM,oBAAoB;gBAAE,GAAG,UAAU;gBAAE,CAAC,QAAQ,EAAE;YAAS;YAE/D,iFAAiF;YACjF,MAAM,oBAAiC,EAAE;YAEzC,iDAAiD;YACjD,OAAO,OAAO,CAAC,mBAAmB,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBACrD,IAAI,MAAM,IAAI,OAAO,IAAI;oBACvB,MAAM,CAAC,OAAO,MAAM,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC;oBAC1C,kBAAkB,IAAI,CAAC;wBACrB,UAAU;wBACV,QAAQ;wBACR;oBACF;gBACF;YACF;YAEA,iDAAiD;YACjD,SAAS;QACX,GAAG;IACL;IAEA,4DAA4D;IAC5D,sEAAsE;IACtE,MAAM,eAAe,QAAQ,MAAM,KAAK;IAExC,gCAAgC;IAChC,kEAAkE;IAClE,qBACE,8OAAC;QAAI,WAAU;kBACZ,wBACC,8OAAC;YAAI,WAAU;sBAAmB;;;;;mBAChC,sBACF,8OAAC;YAAI,WAAU;sBAAiC;;;;;mBAC9C,6BACF,8OAAC;YAAI,WAAU;sBAAkC;;;;;iCAKjD,8OAAC,0HAAA,CAAA,QAAK;YAAC,WAAU;;8BACf,8OAAC,0HAAA,CAAA,cAAW;;sCAEV,8OAAC,0HAAA,CAAA,WAAU;sCACR,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;gCACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;gCAClD,yDAAyD;gCACzD,MAAM,UAAU,aAAa,MAAM,IAAI;gCAEvC,qBACE,8OAAC,0HAAA,CAAA,YAAS;oCAER,SAAS;oCACT,WAAU;8CAET,UAAU,UAAU;mCAJhB,UAAU,EAAE;;;;;4BAOvB;;;;;;wBAID,eAAe,eAAe,kBAC7B,8OAAC,0HAAA,CAAA,WAAU;sCACR,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;gCACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;gCAElD,6EAA6E;gCAC7E,IAAI,aAAa,MAAM,KAAK,GAAG;oCAC7B,qBACE,8OAAC,0HAAA,CAAA,YAAS;wCAER,WAAU;uCADL,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;;;;;gCAMlC;gCAEA,sCAAsC;gCACtC,OAAO,aAAa,GAAG,CAAC,CAAC,yBACvB,8OAAC,0HAAA,CAAA,YAAS;wCAER,WAAU;kDAET,SAAS,UAAU;uCAHf,SAAS,EAAE;;;;;4BAMtB;;;;;;;;;;;;8BAKN,8OAAC,0HAAA,CAAA,YAAS;8BACP,KAAK,MAAM,GAAG,IACb,KAAK,GAAG,CAAC,CAAC,KAAK,yBACb,8OAAC,0HAAA,CAAA,WAAU;4BAET,WAAW,WAAW,MAAM,IAAI,aAAa;sCAG5C,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;gCACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;gCAElD,uDAAuD;gCACvD,IAAI,aAAa,MAAM,KAAK,GAAG;oCAC7B,qBACE,8OAAC,0HAAA,CAAA,YAAS;wCAER,WAAU;kDAEV,cAAA,8OAAC,0HAAA,CAAA,QAAK;4CACJ,OACE,UAAU,CAAC,GAAG,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI;4CAE7C,UAAU,CAAC,IACT,iBACE,UAAU,EAAE,EACZ,IAAI,EAAE,EACN,EAAE,MAAM,CAAC,KAAK;4CAGlB,WAAU;4CACV,UAAU;4CACV,aAAY;;;;;;uCAhBT,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;;;;;gCAoB3C;gCAEA,gDAAgD;gCAChD,OAAO,aAAa,GAAG,CAAC,CAAC,yBACvB,8OAAC,0HAAA,CAAA,YAAS;wCAER,WAAU;kDAEV,cAAA,8OAAC,0HAAA,CAAA,QAAK;4CACJ,OAAO,UAAU,CAAC,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI;4CACjD,UAAU,CAAC,IACT,iBACE,SAAS,EAAE,EACX,IAAI,EAAE,EACN,EAAE,MAAM,CAAC,KAAK;4CAGlB,WAAU;4CACV,UAAU;4CACV,aAAY;;;;;;uCAdT,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;;;;;4BAkB1C;2BAvDK,IAAI,EAAE;;;;oCA2Df,wEAAwE;kCACxE,8OAAC,0HAAA,CAAA,WAAU;kCAER,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;4BACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;4BAElD,uDAAuD;4BACvD,IAAI,aAAa,MAAM,KAAK,GAAG;gCAC7B,qBACE,8OAAC,0HAAA,CAAA,YAAS;oCAER,WAAU;8CAEV,cAAA,8OAAC,0HAAA,CAAA,QAAK;wCACJ,OAAO,UAAU,CAAC,GAAG,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI;wCAC/C,UAAU,CAAC,IACT,iBACE,UAAU,EAAE,EACZ,UACA,EAAE,MAAM,CAAC,KAAK;wCAGlB,WAAU;wCACV,UAAU;wCACV,aAAY;;;;;;mCAdT,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,OAAO,CAAC;;;;;4BAkBxC;4BAEA,gDAAgD;4BAChD,OAAO,aAAa,GAAG,CAAC,CAAC,yBACvB,8OAAC,0HAAA,CAAA,YAAS;oCAER,WAAU;8CAEV,cAAA,8OAAC,0HAAA,CAAA,QAAK;wCACJ,OAAO,UAAU,CAAC,GAAG,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI;wCAC9C,UAAU,CAAC,IACT,iBACE,SAAS,EAAE,EACX,UACA,EAAE,MAAM,CAAC,KAAK;wCAGlB,WAAU;wCACV,UAAU;wCACV,aAAY;;;;;;mCAdT,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,OAAO,CAAC;;;;;wBAkBvC;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 1498, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/api/submission.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\n\r\n// Delete form submission\r\nconst deleteFormSubmission = async (submissionId: number, projectId: number) => {\r\n  try {\r\n    const { data } = await axios.delete(`/form-submissions/${submissionId}?projectId=${projectId}`);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting form submission:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Delete multiple form submissions\r\nconst deleteMultipleFormSubmissions = async (submissionIds: number[], projectId: number) => {\r\n  try {\r\n    const deletePromises = submissionIds.map((id) =>\r\n      axios.delete(`/form-submissions/${id}?projectId=${projectId}`)\r\n    );\r\n    const results = await Promise.all(deletePromises);\r\n    return results.map((result) => result.data);\r\n  } catch (error) {\r\n    console.error(\"Error deleting multiple form submissions:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Update a single answer\r\nconst updateAnswer = async (\r\n  answerData: {\r\n    submissionId: number;\r\n    questionId: number | null | undefined;\r\n    answerType: string;\r\n    value: string | string[] | number;\r\n    questionOptionId?: number | number[] | null;\r\n  },\r\n  projectId: number\r\n) => {\r\n  try {\r\n    if (!answerData.submissionId || !answerData.questionId) {\r\n      throw new Error(\"submissionId and questionId are required\");\r\n    }\r\n\r\n    const formattedData = { ...answerData };\r\n    if (formattedData.questionOptionId === null) {\r\n      delete formattedData.questionOptionId;\r\n    } else if (Array.isArray(formattedData.questionOptionId)) {\r\n      formattedData.questionOptionId = formattedData.questionOptionId.filter((id) => id != null);\r\n      if (formattedData.questionOptionId.length === 0) {\r\n        delete formattedData.questionOptionId;\r\n      }\r\n    }\r\n\r\n    const { data } = await axios.patch(\r\n      `/answers/${answerData.questionId}?projectId=${projectId}`,\r\n      formattedData\r\n    );\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error updating answer:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Update multiple answers for a single question (for select_many type)\r\nconst updateMultipleAnswers = async (answerData: {\r\n  submissionId: number;\r\n  questionId: number;\r\n  answerType: string;\r\n  value: string[];\r\n  questionOptionId?: number[];\r\n}) => {\r\n  try {\r\n    const { data } = await axios.patch(`/answers/${answerData.questionId}`, {\r\n      ...answerData,\r\n      answerType: \"selectmany\",\r\n    });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error updating multiple answers:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Update multiple answers using the /answers/multiple endpoint\r\nconst updateMultipleAnswersWithEndpoint = async (\r\n  answers: Array<{\r\n    id?: number;\r\n    questionId?: number;\r\n    projectId: number;\r\n    value: any;\r\n    answerType: string;\r\n    questionOptionId?: number | number[];\r\n    isOtherOption?: boolean;\r\n    formSubmissionId: number;\r\n  }>,\r\n  projectId: number\r\n) => {\r\n  try {\r\n    const { data } = await axios.patch(`/answers/multiple?projectId=${projectId}`, answers);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error updating multiple answers with endpoint:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport {\r\n  deleteFormSubmission,\r\n  deleteMultipleFormSubmissions,\r\n  updateAnswer,\r\n  updateMultipleAnswers,\r\n  updateMultipleAnswersWithEndpoint,\r\n};"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA,yBAAyB;AACzB,MAAM,uBAAuB,OAAO,cAAsB;IACxD,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,kBAAkB,EAAE,aAAa,WAAW,EAAE,WAAW;QAC9F,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEA,mCAAmC;AACnC,MAAM,gCAAgC,OAAO,eAAyB;IACpE,IAAI;QACF,MAAM,iBAAiB,cAAc,GAAG,CAAC,CAAC,KACxC,4GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,kBAAkB,EAAE,GAAG,WAAW,EAAE,WAAW;QAE/D,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;QAClC,OAAO,QAAQ,GAAG,CAAC,CAAC,SAAW,OAAO,IAAI;IAC5C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,MAAM;IACR;AACF;AAEA,yBAAyB;AACzB,MAAM,eAAe,OACnB,YAOA;IAEA,IAAI;QACF,IAAI,CAAC,WAAW,YAAY,IAAI,CAAC,WAAW,UAAU,EAAE;YACtD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,gBAAgB;YAAE,GAAG,UAAU;QAAC;QACtC,IAAI,cAAc,gBAAgB,KAAK,MAAM;YAC3C,OAAO,cAAc,gBAAgB;QACvC,OAAO,IAAI,MAAM,OAAO,CAAC,cAAc,gBAAgB,GAAG;YACxD,cAAc,gBAAgB,GAAG,cAAc,gBAAgB,CAAC,MAAM,CAAC,CAAC,KAAO,MAAM;YACrF,IAAI,cAAc,gBAAgB,CAAC,MAAM,KAAK,GAAG;gBAC/C,OAAO,cAAc,gBAAgB;YACvC;QACF;QAEA,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAChC,CAAC,SAAS,EAAE,WAAW,UAAU,CAAC,WAAW,EAAE,WAAW,EAC1D;QAEF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEA,uEAAuE;AACvE,MAAM,wBAAwB,OAAO;IAOnC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,WAAW,UAAU,EAAE,EAAE;YACtE,GAAG,UAAU;YACb,YAAY;QACd;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEA,+DAA+D;AAC/D,MAAM,oCAAoC,OACxC,SAUA;IAEA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,4BAA4B,EAAE,WAAW,EAAE;QAC/E,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kDAAkD;QAChE,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 1582, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/api/question-groups.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\n\r\n/**\r\n * Fetch all question groups for a project\r\n */\r\nexport const fetchQuestionGroups = async ({\r\n  projectId,\r\n}: {\r\n  projectId: number;\r\n}) => {\r\n  try {\r\n    // Use the project endpoint to fetch the project with its question groups\r\n    const { data } = await axios.get(`/projects/form/${projectId}`);\r\n\r\n    // Extract question groups from the project data\r\n    const questionGroups = data.data?.project?.questionGroup || [];\r\n    return questionGroups;\r\n  } catch (error) {\r\n    console.error(\r\n      \"Error fetching question groups from project endpoint:\",\r\n      error\r\n    );\r\n\r\n    // Fallback to direct question groups endpoint\r\n    try {\r\n      const { data } = await axios.post(`/question-groups`, { projectId });\r\n      return data.data?.projectGroup || [];\r\n    } catch (fallbackError) {\r\n      console.error(\"Error in fallback fetch:\", fallbackError);\r\n\r\n      // Last resort: create a dummy group for debugging\r\n      if (process.env.NODE_ENV === \"development\") {\r\n        return [];\r\n      }\r\n\r\n      return [];\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Create a new question group\r\n */\r\nexport const createQuestionGroup = async ({\r\n  title,\r\n  order,\r\n  projectId,\r\n  selectedQuestionIds,\r\n}: {\r\n  title: string;\r\n  order: number;\r\n  projectId: number;\r\n  selectedQuestionIds?: number[];\r\n}) => {\r\n  try {\r\n    const { data } = await axios.post(`/question-groups`, {\r\n      title,\r\n      order,\r\n      projectId,\r\n      selectedQuestionIds: selectedQuestionIds || [],\r\n    });\r\n\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error creating question group:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Update an existing question group\r\n */\r\nexport const updateQuestionGroup = async ({\r\n  id,\r\n  title,\r\n  order,\r\n  selectedQuestionIds,\r\n}: {\r\n  id: number;\r\n  title: string;\r\n  order: number;\r\n  selectedQuestionIds?: number[];\r\n}) => {\r\n  try {\r\n    const { data } = await axios.patch(`/question-groups`, {\r\n      id,\r\n      title,\r\n      order,\r\n      selectedQuestionIds,\r\n    });\r\n\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error updating question group:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Delete a question group\r\n */\r\nexport const deleteQuestionGroup = async ({ id }: { id: number }) => {\r\n  try {\r\n    const { data } = await axios.delete(`/question-groups/${id}`);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting question group:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Delete a question group and all its questions\r\n */\r\nexport const deleteQuestionAndGroup = async ({ id }: { id: number }) => {\r\n  try {\r\n    const { data } = await axios.delete(\r\n      `/question-groups/group/question/${id}`\r\n    );\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting question group and questions:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Remove a question from a group\r\n */\r\nexport const removeQuestionFromGroup = async ({\r\n  groupId,\r\n  questionId,\r\n}: {\r\n  groupId: number;\r\n  questionId: number;\r\n}) => {\r\n  const { data } = await axios.patch(`/question-groups/question/remove`, {\r\n    groupId,\r\n    questionId,\r\n  });\r\n  return data;\r\n};\r\n\r\n/**\r\n * Move a question from one group to another\r\n */\r\nexport const moveQuestionBetweenGroups = async ({\r\n  groupId,\r\n  newGroupId,\r\n  questionId,\r\n}: {\r\n  groupId: number;\r\n  newGroupId: number;\r\n  questionId: number;\r\n}) => {\r\n  const { data } = await axios.patch(`/question-groups/question/move`, {\r\n    groupId,\r\n    newGroupId,\r\n    questionId,\r\n  });\r\n  return data;\r\n};\r\n\r\n// Parent group functionality removed for simplicity\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAKO,MAAM,sBAAsB,OAAO,EACxC,SAAS,EAGV;IACC,IAAI;QACF,yEAAyE;QACzE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW;QAE9D,gDAAgD;QAChD,MAAM,iBAAiB,KAAK,IAAI,EAAE,SAAS,iBAAiB,EAAE;QAC9D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CACX,yDACA;QAGF,8CAA8C;QAC9C,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,EAAE;gBAAE;YAAU;YAClE,OAAO,KAAK,IAAI,EAAE,gBAAgB,EAAE;QACtC,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,kDAAkD;YAClD,wCAA4C;gBAC1C,OAAO,EAAE;YACX;;QAGF;IACF;AACF;AAKO,MAAM,sBAAsB,OAAO,EACxC,KAAK,EACL,KAAK,EACL,SAAS,EACT,mBAAmB,EAMpB;IACC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,EAAE;YACpD;YACA;YACA;YACA,qBAAqB,uBAAuB,EAAE;QAChD;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAKO,MAAM,sBAAsB,OAAO,EACxC,EAAE,EACF,KAAK,EACL,KAAK,EACL,mBAAmB,EAMpB;IACC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,gBAAgB,CAAC,EAAE;YACrD;YACA;YACA;YACA;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAKO,MAAM,sBAAsB,OAAO,EAAE,EAAE,EAAkB;IAC9D,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,IAAI;QAC5D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAKO,MAAM,yBAAyB,OAAO,EAAE,EAAE,EAAkB;IACjE,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,MAAM,CACjC,CAAC,gCAAgC,EAAE,IAAI;QAEzC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM;IACR;AACF;AAKO,MAAM,0BAA0B,OAAO,EAC5C,OAAO,EACP,UAAU,EAIX;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAE;QACrE;QACA;IACF;IACA,OAAO;AACT;AAKO,MAAM,4BAA4B,OAAO,EAC9C,OAAO,EACP,UAAU,EACV,UAAU,EAKX;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,8BAA8B,CAAC,EAAE;QACnE;QACA;QACA;IACF;IACA,OAAO;AACT,GAEA,oDAAoD", "debugId": null}}, {"offset": {"line": 1685, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/api/projects.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\nimport { Project } from \"@/types\";\r\n\r\nconst fetchProjectById = async ({ projectId }: { projectId: number }) => {\r\n  const { data } = await axios.get(`/projects/${projectId}`);\r\n  return data.project;\r\n};\r\n\r\nconst createProjectFromTemplate = async (dataToSend: {\r\n  templateId: number;\r\n  name: string;\r\n  description: string;\r\n  sector: string;\r\n  country: string;\r\n}) => {\r\n  const { data } = await axios.post(`/projects/from-template`, dataToSend);\r\n  return data;\r\n};\r\n\r\n//Fetch all projects for the current user\r\nconst fetchProjects = async (): Promise<Project[]> => {\r\n  try {\r\n    const { data } = await axios.get(`/projects`);\r\n    return data.projects;\r\n  } catch (error) {\r\n    console.error(\"Error fetching projects:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Delete project\r\nconst deleteProject = async (projectId: number) => {\r\n  const { data } = await axios.delete(`/projects/delete/${projectId}`);\r\n  return data;\r\n};\r\n\r\n// Delete multiple projects\r\nconst deleteMultipleProjects = async (projectIds: number[]) => {\r\n  try {\r\n    const { data } = await axios.delete(`/projects/delete-multiple`, {\r\n      data: { projectIds },\r\n    });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting multiple projects:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n//Archive project\r\nconst archiveProject = async (projectId: number) => {\r\n  try {\r\n    const { data } = await axios.patch(`/projects/change-status/${projectId}`, {\r\n      status: \"archived\",\r\n    });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error archiving project:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n//Deploy project\r\nconst deployProject = async (\r\n  projectId: number,\r\n  isUnarchive: boolean = false\r\n) => {\r\n  try {\r\n    const { data } = await axios.patch(`/projects/change-status/${projectId}`, {\r\n      status: \"deployed\",\r\n    });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deploying project:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Archive multiple projects\r\nconst archiveMultipleProjects = async (projectIds: number[]) => {\r\n  try {\r\n    const { data } = await axios.patch(`/projects/update-many-status`, {\r\n      projectIds,\r\n      status: \"archived\",\r\n    });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error archiving multiple projects:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Check if user exists by email\r\nconst checkUserExists = async (email: string) => {\r\n  try {\r\n    const { data } = await axios.post(`/users/check-email`, { email });\r\n    return data;\r\n  } catch (error: any) {\r\n    // Format error message consistently\r\n    const errorMessage =\r\n      typeof error.response?.data?.message === \"object\"\r\n        ? JSON.stringify(error.response?.data?.message)\r\n        : error.response?.data?.message ||\r\n          error.message ||\r\n          \"Failed to check user\";\r\n\r\n    throw new Error(errorMessage);\r\n  }\r\n};\r\n\r\n// Add user to project by email\r\nconst addProjectUser = async ({\r\n  projectId,\r\n  email,\r\n  permissions,\r\n}: {\r\n  projectId: number;\r\n  email: string;\r\n  permissions: Record<string, boolean>;\r\n}) => {\r\n  try {\r\n    // First check if the user exists\r\n    const userData = await checkUserExists(email);\r\n\r\n    if (!userData || !userData.success) {\r\n      throw new Error(userData?.message || \"User not found\");\r\n    }\r\n\r\n    // Now use the user ID to add them to the project\r\n    const { data } = await axios.post(`/project-users`, {\r\n      userId: userData.user.id,\r\n      projectId,\r\n      permission: permissions,\r\n    });\r\n\r\n    return data;\r\n  } catch (error: any) {\r\n    console.error(\"Error adding user to project:\", error);\r\n    // Format error message as a string\r\n    const errorMessage =\r\n      typeof error.response?.data?.message === \"object\"\r\n        ? JSON.stringify(error.response?.data?.message)\r\n        : error.response?.data?.message ||\r\n          error.message ||\r\n          \"Failed to add user\";\r\n\r\n    throw new Error(errorMessage);\r\n  }\r\n};\r\n\r\n// Fetch all users for a specific project\r\nconst fetchProjectUsers = async (projectId: number) => {\r\n  try {\r\n    const { data } = await axios.get(`/project-users/${projectId}`);\r\n    return data.data.AllUser;\r\n  } catch (error: any) {\r\n    console.error(\"Error fetching project users:\", error);\r\n    const errorMessage =\r\n      typeof error.response?.data?.message === \"object\"\r\n        ? JSON.stringify(error.response?.data?.message)\r\n        : error.response?.data?.message ||\r\n          error.message ||\r\n          \"Failed to fetch project users\";\r\n\r\n    throw new Error(errorMessage);\r\n  }\r\n};\r\n\r\n// Create answer submission\r\nconst createAnswerSubmission = async (\r\n  answers: {\r\n    projectId: number;\r\n    questionId: number;\r\n    answerType: string;\r\n    value?: string | number | boolean;\r\n    imageUrl?: string;\r\n    questionOptionId?: number | number[];\r\n    isOtherOption?: boolean;\r\n  }[]\r\n) => {\r\n  try {\r\n    const { data } = await axios.post(`/answers/multiple`, answers);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error creating answer submission:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Update answer submission\r\nconst updateAnswerSubmission = async (\r\n  projectId: number,\r\n  submissionId: number,\r\n  answers: {\r\n    projectId: number;\r\n    questionId: number;\r\n    answerType: string;\r\n    value?: string | number | boolean;\r\n    imageUrl?: string;\r\n    questionOptionId?: number | number[];\r\n    isOtherOption?: boolean;\r\n  }[]\r\n) => {\r\n  try {\r\n    const { data } = await axios.patch(`/form-submissions/${projectId}/${submissionId}`, { answers });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error updating answer submission:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n\r\n\r\n\r\nexport {\r\n  fetchProjectById,\r\n  fetchProjects,\r\n  deleteProject,\r\n  deleteMultipleProjects,\r\n  archiveMultipleProjects,\r\n  createProjectFromTemplate,\r\n  archiveProject,\r\n  deployProject,\r\n  addProjectUser,\r\n  checkUserExists,\r\n  fetchProjectUsers,\r\n  createAnswerSubmission,\r\n  updateAnswerSubmission,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;AAGA,MAAM,mBAAmB,OAAO,EAAE,SAAS,EAAyB;IAClE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW;IACzD,OAAO,KAAK,OAAO;AACrB;AAEA,MAAM,4BAA4B,OAAO;IAOvC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,EAAE;IAC7D,OAAO;AACT;AAEA,yCAAyC;AACzC,MAAM,gBAAgB;IACpB,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;QAC5C,OAAO,KAAK,QAAQ;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEA,iBAAiB;AACjB,MAAM,gBAAgB,OAAO;IAC3B,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,WAAW;IACnE,OAAO;AACT;AAEA,2BAA2B;AAC3B,MAAM,yBAAyB,OAAO;IACpC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,yBAAyB,CAAC,EAAE;YAC/D,MAAM;gBAAE;YAAW;QACrB;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF;AAEA,iBAAiB;AACjB,MAAM,iBAAiB,OAAO;IAC5B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,wBAAwB,EAAE,WAAW,EAAE;YACzE,QAAQ;QACV;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEA,gBAAgB;AAChB,MAAM,gBAAgB,OACpB,WACA,cAAuB,KAAK;IAE5B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,wBAAwB,EAAE,WAAW,EAAE;YACzE,QAAQ;QACV;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEA,4BAA4B;AAC5B,MAAM,0BAA0B,OAAO;IACrC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE;YACjE;YACA,QAAQ;QACV;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM;IACR;AACF;AAEA,gCAAgC;AAChC,MAAM,kBAAkB,OAAO;IAC7B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,EAAE;YAAE;QAAM;QAChE,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,oCAAoC;QACpC,MAAM,eACJ,OAAO,MAAM,QAAQ,EAAE,MAAM,YAAY,WACrC,KAAK,SAAS,CAAC,MAAM,QAAQ,EAAE,MAAM,WACrC,MAAM,QAAQ,EAAE,MAAM,WACtB,MAAM,OAAO,IACb;QAEN,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,+BAA+B;AAC/B,MAAM,iBAAiB,OAAO,EAC5B,SAAS,EACT,KAAK,EACL,WAAW,EAKZ;IACC,IAAI;QACF,iCAAiC;QACjC,MAAM,WAAW,MAAM,gBAAgB;QAEvC,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;YAClC,MAAM,IAAI,MAAM,UAAU,WAAW;QACvC;QAEA,iDAAiD;QACjD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE;YAClD,QAAQ,SAAS,IAAI,CAAC,EAAE;YACxB;YACA,YAAY;QACd;QAEA,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,mCAAmC;QACnC,MAAM,eACJ,OAAO,MAAM,QAAQ,EAAE,MAAM,YAAY,WACrC,KAAK,SAAS,CAAC,MAAM,QAAQ,EAAE,MAAM,WACrC,MAAM,QAAQ,EAAE,MAAM,WACtB,MAAM,OAAO,IACb;QAEN,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,yCAAyC;AACzC,MAAM,oBAAoB,OAAO;IAC/B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW;QAC9D,OAAO,KAAK,IAAI,CAAC,OAAO;IAC1B,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM,eACJ,OAAO,MAAM,QAAQ,EAAE,MAAM,YAAY,WACrC,KAAK,SAAS,CAAC,MAAM,QAAQ,EAAE,MAAM,WACrC,MAAM,QAAQ,EAAE,MAAM,WACtB,MAAM,OAAO,IACb;QAEN,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,2BAA2B;AAC3B,MAAM,yBAAyB,OAC7B;IAUA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,EAAE;QACvD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF;AAEA,2BAA2B;AAC3B,MAAM,yBAAyB,OAC7B,WACA,cACA;IAUA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,kBAAkB,EAAE,UAAU,CAAC,EAAE,cAAc,EAAE;YAAE;QAAQ;QAC/F,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 1851, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/conditionalQuestions.ts"], "sourcesContent": ["import { Question } from \"@/types/formBuilder\";\r\n\r\n/**\r\n * Utility functions for handling conditional questions logic\r\n */\r\n\r\n/**\r\n * Get the next question ID based on the selected option\r\n */\r\nexport const getNextQuestionId = (\r\n  question: Question,\r\n  selectedValue: string | string[]\r\n): number | null => {\r\n  if (!question.questionOptions || question.questionOptions.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  // For selectone, selectedValue is a string\r\n  if (question.inputType === \"selectone\" && typeof selectedValue === \"string\") {\r\n    const selectedOption = question.questionOptions.find(\r\n      (option) => option.label === selectedValue\r\n    );\r\n    return selectedOption?.nextQuestionId || null;\r\n  }\r\n\r\n  // For selectmany, selectedValue is an array - return the first next question found\r\n  if (question.inputType === \"selectmany\" && Array.isArray(selectedValue)) {\r\n    for (const value of selectedValue) {\r\n      const selectedOption = question.questionOptions.find(\r\n        (option) => option.label === value\r\n      );\r\n      if (selectedOption?.nextQuestionId) {\r\n        return selectedOption.nextQuestionId;\r\n      }\r\n    }\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\n/**\r\n * Get all possible next question IDs for a question (for dependency tracking)\r\n */\r\nexport const getAllNextQuestionIds = (question: Question): number[] => {\r\n  if (!question.questionOptions || question.questionOptions.length === 0) {\r\n    return [];\r\n  }\r\n\r\n  return question.questionOptions\r\n    .map((option) => option.nextQuestionId)\r\n    .filter((id): id is number => id !== null && id !== undefined);\r\n};\r\n\r\n/**\r\n * Determine which questions should be visible based on current answers\r\n */\r\nexport const getVisibleQuestions = (\r\n  allQuestions: Question[],\r\n  answers: Record<string, any>\r\n): Question[] => {\r\n  const visibleQuestionIds = new Set<number>();\r\n  const conditionalQuestionIds = new Set<number>();\r\n\r\n  // First, collect all questions that are conditional (have a parent question)\r\n  allQuestions.forEach((question) => {\r\n    const nextQuestionIds = getAllNextQuestionIds(question);\r\n    nextQuestionIds.forEach((id) => conditionalQuestionIds.add(id));\r\n  });\r\n\r\n  // Start with all non-conditional questions (questions that are not triggered by other questions)\r\n  allQuestions.forEach((question) => {\r\n    if (!conditionalQuestionIds.has(question.id)) {\r\n      visibleQuestionIds.add(question.id);\r\n    }\r\n  });\r\n\r\n  // Process answers to determine which conditional questions should be visible\r\n  Object.entries(answers).forEach(([questionIdStr, answer]) => {\r\n    const questionId = parseInt(questionIdStr);\r\n    const question = allQuestions.find((q) => q.id === questionId);\r\n\r\n    if (question && answer) {\r\n      const nextQuestionId = getNextQuestionId(question, answer);\r\n      if (nextQuestionId) {\r\n        visibleQuestionIds.add(nextQuestionId);\r\n      }\r\n    }\r\n  });\r\n\r\n  // Return questions in their original order, filtered by visibility\r\n  return allQuestions.filter((question) => visibleQuestionIds.has(question.id));\r\n};\r\n\r\n/**\r\n * Check if a question should be visible based on current answers\r\n */\r\nexport const isQuestionVisible = (\r\n  question: Question,\r\n  allQuestions: Question[],\r\n  answers: Record<string, any>\r\n): boolean => {\r\n  const visibleQuestions = getVisibleQuestions(allQuestions, answers);\r\n  return visibleQuestions.some((q) => q.id === question.id);\r\n};\r\n\r\n/**\r\n * Get questions that depend on a specific question\r\n */\r\nexport const getDependentQuestions = (\r\n  parentQuestionId: number,\r\n  allQuestions: Question[]\r\n): Question[] => {\r\n  const parentQuestion = allQuestions.find((q) => q.id === parentQuestionId);\r\n  if (!parentQuestion) return [];\r\n\r\n  const nextQuestionIds = getAllNextQuestionIds(parentQuestion);\r\n  return allQuestions.filter((q) => nextQuestionIds.includes(q.id));\r\n};\r\n\r\n/**\r\n * Check if a question is a follow-up question (has a parent question)\r\n */\r\nexport const isFollowUpQuestion = (\r\n  questionId: number,\r\n  allQuestions: Question[]\r\n): boolean => {\r\n  return allQuestions.some((question) => {\r\n    const nextQuestionIds = getAllNextQuestionIds(question);\r\n    return nextQuestionIds.includes(questionId);\r\n  });\r\n};\r\n\r\n/**\r\n * Get the parent question for a follow-up question\r\n */\r\nexport const getParentQuestion = (\r\n  questionId: number,\r\n  allQuestions: Question[]\r\n): Question | null => {\r\n  return (\r\n    allQuestions.find((question) => {\r\n      const nextQuestionIds = getAllNextQuestionIds(question);\r\n      return nextQuestionIds.includes(questionId);\r\n    }) || null\r\n  );\r\n};\r\n\r\n/**\r\n * Get questions in nested structure for rendering\r\n * Returns questions grouped by parent-child relationships, maintaining order\r\n */\r\nexport const getNestedQuestions = (\r\n  allQuestions: Question[],\r\n  answers: Record<string, any>\r\n): Array<{\r\n  question: Question;\r\n  isVisible: boolean;\r\n  isFollowUp: boolean;\r\n  parentQuestion?: Question;\r\n  followUps: Array<{\r\n    question: Question;\r\n    isVisible: boolean;\r\n  }>;\r\n}> => {\r\n  const visibleQuestions = getVisibleQuestions(allQuestions, answers);\r\n  const visibleQuestionIds = new Set(visibleQuestions.map((q) => q.id));\r\n\r\n  // Get all parent questions (questions that are not follow-ups themselves)\r\n  const parentQuestions = allQuestions.filter(\r\n    (question) => !isFollowUpQuestion(question.id, allQuestions)\r\n  );\r\n\r\n  // Sort parent questions by position to maintain order\r\n  const sortedParentQuestions = parentQuestions.sort(\r\n    (a, b) => a.position - b.position\r\n  );\r\n\r\n  return sortedParentQuestions\r\n    .map((parentQuestion) => {\r\n      const followUpQuestions = getDependentQuestions(\r\n        parentQuestion.id,\r\n        allQuestions\r\n      );\r\n      const sortedFollowUps = followUpQuestions.sort(\r\n        (a, b) => a.position - b.position\r\n      );\r\n\r\n      return {\r\n        question: parentQuestion,\r\n        isVisible: visibleQuestionIds.has(parentQuestion.id),\r\n        isFollowUp: false,\r\n        followUps: sortedFollowUps.map((followUp) => ({\r\n          question: followUp,\r\n          isVisible: visibleQuestionIds.has(followUp.id),\r\n        })),\r\n      };\r\n    })\r\n    .filter(\r\n      (group) => group.isVisible || group.followUps.some((f) => f.isVisible)\r\n    );\r\n};\r\n\r\n/**\r\n * Clean up answers for questions that are no longer visible\r\n */\r\nexport const cleanupHiddenAnswers = (\r\n  answers: Record<string, any>,\r\n  visibleQuestions: Question[]\r\n): Record<string, any> => {\r\n  const visibleQuestionIds = new Set(visibleQuestions.map((q) => q.id));\r\n  const cleanedAnswers: Record<string, any> = {};\r\n\r\n  Object.entries(answers).forEach(([questionId, answer]) => {\r\n    if (visibleQuestionIds.has(parseInt(questionId))) {\r\n      cleanedAnswers[questionId] = answer;\r\n    }\r\n  });\r\n\r\n  return cleanedAnswers;\r\n};\r\n\r\n/**\r\n * Clean up answers for questions that are no longer visible while preserving original data\r\n * This version maintains original submitted values for conditional questions\r\n */\r\nexport const cleanupHiddenAnswersWithPersistence = (\r\n  currentAnswers: Record<string, any>,\r\n  visibleQuestions: Question[],\r\n  originalAnswers: Record<string, any>\r\n): Record<string, any> => {\r\n  const visibleQuestionIds = new Set(visibleQuestions.map((q) => q.id));\r\n  const cleanedAnswers: Record<string, any> = {};\r\n\r\n  Object.entries(currentAnswers).forEach(([questionId, answer]) => {\r\n    if (visibleQuestionIds.has(parseInt(questionId))) {\r\n      cleanedAnswers[questionId] = answer;\r\n    }\r\n  });\r\n\r\n  return cleanedAnswers;\r\n};\r\n\r\n/**\r\n * Restore original answers for questions that have become visible again\r\n * This helps maintain data persistence when toggling conditional questions\r\n */\r\nexport const restoreOriginalAnswers = (\r\n  currentAnswers: Record<string, any>,\r\n  visibleQuestions: Question[],\r\n  originalAnswers: Record<string, any>\r\n): Record<string, any> => {\r\n  const restoredAnswers = { ...currentAnswers };\r\n  const visibleQuestionIds = new Set(visibleQuestions.map((q) => q.id));\r\n\r\n  // For each visible question, if it doesn't have a current answer but has an original answer, restore it\r\n  visibleQuestionIds.forEach((questionId) => {\r\n    const questionIdStr = questionId.toString();\r\n    const hasCurrentAnswer = currentAnswers[questionIdStr] !== undefined &&\r\n                            currentAnswers[questionIdStr] !== \"\" &&\r\n                            !(Array.isArray(currentAnswers[questionIdStr]) && currentAnswers[questionIdStr].length === 0);\r\n    const hasOriginalAnswer = originalAnswers[questionIdStr] !== undefined &&\r\n                             originalAnswers[questionIdStr] !== \"\" &&\r\n                             !(Array.isArray(originalAnswers[questionIdStr]) && originalAnswers[questionIdStr].length === 0);\r\n\r\n    // If the question is visible but has no current answer, restore the original answer\r\n    if (!hasCurrentAnswer && hasOriginalAnswer) {\r\n      restoredAnswers[questionIdStr] = originalAnswers[questionIdStr];\r\n    }\r\n  });\r\n\r\n  return restoredAnswers;\r\n};\r\n\r\n/**\r\n * Validate that all visible required questions have answers\r\n */\r\nexport const validateVisibleQuestions = (\r\n  visibleQuestions: Question[],\r\n  answers: Record<string, any>\r\n): Record<string, string> => {\r\n  const errors: Record<string, string> = {};\r\n\r\n  visibleQuestions.forEach((question) => {\r\n    if (question.isRequired) {\r\n      const value = answers[question.id];\r\n      if (\r\n        (typeof value === \"string\" && !value.trim()) ||\r\n        (Array.isArray(value) && value.length === 0) ||\r\n        value === undefined ||\r\n        value === null\r\n      ) {\r\n        errors[question.id] = `${question.label} is required`;\r\n      }\r\n    }\r\n  });\r\n\r\n  return errors;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AASO,MAAM,oBAAoB,CAC/B,UACA;IAEA,IAAI,CAAC,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,MAAM,KAAK,GAAG;QACtE,OAAO;IACT;IAEA,2CAA2C;IAC3C,IAAI,SAAS,SAAS,KAAK,eAAe,OAAO,kBAAkB,UAAU;QAC3E,MAAM,iBAAiB,SAAS,eAAe,CAAC,IAAI,CAClD,CAAC,SAAW,OAAO,KAAK,KAAK;QAE/B,OAAO,gBAAgB,kBAAkB;IAC3C;IAEA,mFAAmF;IACnF,IAAI,SAAS,SAAS,KAAK,gBAAgB,MAAM,OAAO,CAAC,gBAAgB;QACvE,KAAK,MAAM,SAAS,cAAe;YACjC,MAAM,iBAAiB,SAAS,eAAe,CAAC,IAAI,CAClD,CAAC,SAAW,OAAO,KAAK,KAAK;YAE/B,IAAI,gBAAgB,gBAAgB;gBAClC,OAAO,eAAe,cAAc;YACtC;QACF;IACF;IAEA,OAAO;AACT;AAKO,MAAM,wBAAwB,CAAC;IACpC,IAAI,CAAC,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,MAAM,KAAK,GAAG;QACtE,OAAO,EAAE;IACX;IAEA,OAAO,SAAS,eAAe,CAC5B,GAAG,CAAC,CAAC,SAAW,OAAO,cAAc,EACrC,MAAM,CAAC,CAAC,KAAqB,OAAO,QAAQ,OAAO;AACxD;AAKO,MAAM,sBAAsB,CACjC,cACA;IAEA,MAAM,qBAAqB,IAAI;IAC/B,MAAM,yBAAyB,IAAI;IAEnC,6EAA6E;IAC7E,aAAa,OAAO,CAAC,CAAC;QACpB,MAAM,kBAAkB,sBAAsB;QAC9C,gBAAgB,OAAO,CAAC,CAAC,KAAO,uBAAuB,GAAG,CAAC;IAC7D;IAEA,iGAAiG;IACjG,aAAa,OAAO,CAAC,CAAC;QACpB,IAAI,CAAC,uBAAuB,GAAG,CAAC,SAAS,EAAE,GAAG;YAC5C,mBAAmB,GAAG,CAAC,SAAS,EAAE;QACpC;IACF;IAEA,6EAA6E;IAC7E,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,eAAe,OAAO;QACtD,MAAM,aAAa,SAAS;QAC5B,MAAM,WAAW,aAAa,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;QAEnD,IAAI,YAAY,QAAQ;YACtB,MAAM,iBAAiB,kBAAkB,UAAU;YACnD,IAAI,gBAAgB;gBAClB,mBAAmB,GAAG,CAAC;YACzB;QACF;IACF;IAEA,mEAAmE;IACnE,OAAO,aAAa,MAAM,CAAC,CAAC,WAAa,mBAAmB,GAAG,CAAC,SAAS,EAAE;AAC7E;AAKO,MAAM,oBAAoB,CAC/B,UACA,cACA;IAEA,MAAM,mBAAmB,oBAAoB,cAAc;IAC3D,OAAO,iBAAiB,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,SAAS,EAAE;AAC1D;AAKO,MAAM,wBAAwB,CACnC,kBACA;IAEA,MAAM,iBAAiB,aAAa,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;IACzD,IAAI,CAAC,gBAAgB,OAAO,EAAE;IAE9B,MAAM,kBAAkB,sBAAsB;IAC9C,OAAO,aAAa,MAAM,CAAC,CAAC,IAAM,gBAAgB,QAAQ,CAAC,EAAE,EAAE;AACjE;AAKO,MAAM,qBAAqB,CAChC,YACA;IAEA,OAAO,aAAa,IAAI,CAAC,CAAC;QACxB,MAAM,kBAAkB,sBAAsB;QAC9C,OAAO,gBAAgB,QAAQ,CAAC;IAClC;AACF;AAKO,MAAM,oBAAoB,CAC/B,YACA;IAEA,OACE,aAAa,IAAI,CAAC,CAAC;QACjB,MAAM,kBAAkB,sBAAsB;QAC9C,OAAO,gBAAgB,QAAQ,CAAC;IAClC,MAAM;AAEV;AAMO,MAAM,qBAAqB,CAChC,cACA;IAWA,MAAM,mBAAmB,oBAAoB,cAAc;IAC3D,MAAM,qBAAqB,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;IAEnE,0EAA0E;IAC1E,MAAM,kBAAkB,aAAa,MAAM,CACzC,CAAC,WAAa,CAAC,mBAAmB,SAAS,EAAE,EAAE;IAGjD,sDAAsD;IACtD,MAAM,wBAAwB,gBAAgB,IAAI,CAChD,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IAGnC,OAAO,sBACJ,GAAG,CAAC,CAAC;QACJ,MAAM,oBAAoB,sBACxB,eAAe,EAAE,EACjB;QAEF,MAAM,kBAAkB,kBAAkB,IAAI,CAC5C,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;QAGnC,OAAO;YACL,UAAU;YACV,WAAW,mBAAmB,GAAG,CAAC,eAAe,EAAE;YACnD,YAAY;YACZ,WAAW,gBAAgB,GAAG,CAAC,CAAC,WAAa,CAAC;oBAC5C,UAAU;oBACV,WAAW,mBAAmB,GAAG,CAAC,SAAS,EAAE;gBAC/C,CAAC;QACH;IACF,GACC,MAAM,CACL,CAAC,QAAU,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,SAAS;AAE3E;AAKO,MAAM,uBAAuB,CAClC,SACA;IAEA,MAAM,qBAAqB,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;IACnE,MAAM,iBAAsC,CAAC;IAE7C,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,YAAY,OAAO;QACnD,IAAI,mBAAmB,GAAG,CAAC,SAAS,cAAc;YAChD,cAAc,CAAC,WAAW,GAAG;QAC/B;IACF;IAEA,OAAO;AACT;AAMO,MAAM,sCAAsC,CACjD,gBACA,kBACA;IAEA,MAAM,qBAAqB,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;IACnE,MAAM,iBAAsC,CAAC;IAE7C,OAAO,OAAO,CAAC,gBAAgB,OAAO,CAAC,CAAC,CAAC,YAAY,OAAO;QAC1D,IAAI,mBAAmB,GAAG,CAAC,SAAS,cAAc;YAChD,cAAc,CAAC,WAAW,GAAG;QAC/B;IACF;IAEA,OAAO;AACT;AAMO,MAAM,yBAAyB,CACpC,gBACA,kBACA;IAEA,MAAM,kBAAkB;QAAE,GAAG,cAAc;IAAC;IAC5C,MAAM,qBAAqB,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;IAEnE,wGAAwG;IACxG,mBAAmB,OAAO,CAAC,CAAC;QAC1B,MAAM,gBAAgB,WAAW,QAAQ;QACzC,MAAM,mBAAmB,cAAc,CAAC,cAAc,KAAK,aACnC,cAAc,CAAC,cAAc,KAAK,MAClC,CAAC,CAAC,MAAM,OAAO,CAAC,cAAc,CAAC,cAAc,KAAK,cAAc,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC;QACpH,MAAM,oBAAoB,eAAe,CAAC,cAAc,KAAK,aACpC,eAAe,CAAC,cAAc,KAAK,MACnC,CAAC,CAAC,MAAM,OAAO,CAAC,eAAe,CAAC,cAAc,KAAK,eAAe,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC;QAEvH,oFAAoF;QACpF,IAAI,CAAC,oBAAoB,mBAAmB;YAC1C,eAAe,CAAC,cAAc,GAAG,eAAe,CAAC,cAAc;QACjE;IACF;IAEA,OAAO;AACT;AAKO,MAAM,2BAA2B,CACtC,kBACA;IAEA,MAAM,SAAiC,CAAC;IAExC,iBAAiB,OAAO,CAAC,CAAC;QACxB,IAAI,SAAS,UAAU,EAAE;YACvB,MAAM,QAAQ,OAAO,CAAC,SAAS,EAAE,CAAC;YAClC,IACE,AAAC,OAAO,UAAU,YAAY,CAAC,MAAM,IAAI,MACxC,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK,KAC1C,UAAU,aACV,UAAU,MACV;gBACA,MAAM,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,SAAS,KAAK,CAAC,YAAY,CAAC;YACvD;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2017, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/form-inputs/NestedQuestionRenderer.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Question } from \"@/types/formBuilder\";\r\nimport { Label } from \"@/components/ui/label\";\r\n\r\ninterface NestedQuestionRendererProps {\r\n  questionGroup: {\r\n    question: Question;\r\n    isVisible: boolean;\r\n    isFollowUp: boolean;\r\n    followUps: Array<{\r\n      question: Question;\r\n      isVisible: boolean;\r\n    }>;\r\n  };\r\n  renderQuestionInput: (question: Question) => React.ReactNode;\r\n  errors: Record<number, string>;\r\n  className?: string;\r\n}\r\n\r\nconst NestedQuestionRenderer: React.FC<NestedQuestionRendererProps> = ({\r\n  questionGroup,\r\n  renderQuestionInput,\r\n  errors,\r\n  className = \"\",\r\n}) => {\r\n  const { question: parentQuestion, isVisible: isParentVisible, followUps } = questionGroup;\r\n  \r\n  // Don't render anything if neither parent nor any follow-ups are visible\r\n  if (!isParentVisible && !followUps.some(f => f.isVisible)) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className={`${className}`}>\r\n      {/* Parent Question */}\r\n      {isParentVisible && (\r\n        <div className=\"border border-neutral-500 dark:border-gray-700 rounded-md p-4 bg-neutral-100 dark:bg-gray-800\">\r\n          <div className=\"mb-2\">\r\n            <Label className=\"text-base font-medium\">\r\n              {parentQuestion.label}\r\n              {parentQuestion.isRequired && (\r\n                <span className=\"text-red-500 ml-1\">*</span>\r\n              )}\r\n            </Label>\r\n            {parentQuestion.hint && (\r\n              <p className=\"text-sm text-muted-foreground mt-1\">\r\n                {parentQuestion.hint}\r\n              </p>\r\n            )}\r\n            {errors[parentQuestion.id] && (\r\n              <p className=\"text-sm text-red-500 mt-1\">\r\n                {errors[parentQuestion.id]}\r\n              </p>\r\n            )}\r\n          </div>\r\n          <div className=\"mt-2\">{renderQuestionInput(parentQuestion)}</div>\r\n          \r\n          {/* Follow-up Questions */}\r\n          {followUps.some(f => f.isVisible) && (\r\n            <div className=\"mt-4 ml-4 space-y-3 border-l-2 border-blue-200 dark:border-blue-700 pl-4\">\r\n              {followUps.map(({ question: followUpQuestion, isVisible: isFollowUpVisible }) => (\r\n                isFollowUpVisible && (\r\n                  <div\r\n                    key={followUpQuestion.id}\r\n                    className=\"border border-gray-100 dark:border-gray-600 rounded-md p-3 bg-blue-50 dark:bg-blue-900/20\"\r\n                  >\r\n                    <div className=\"mb-2\">\r\n                      <Label className=\"text-sm font-medium text-blue-900 dark:text-blue-100\">\r\n                        {followUpQuestion.label}\r\n                        {followUpQuestion.isRequired && (\r\n                          <span className=\"text-red-500 ml-1\">*</span>\r\n                        )}\r\n                      </Label>\r\n                      {followUpQuestion.hint && (\r\n                        <p className=\"text-xs text-blue-700 dark:text-blue-300 mt-1\">\r\n                          {followUpQuestion.hint}\r\n                        </p>\r\n                      )}\r\n                      {errors[followUpQuestion.id] && (\r\n                        <p className=\"text-xs text-red-500 mt-1\">\r\n                          {errors[followUpQuestion.id]}\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"mt-2\">{renderQuestionInput(followUpQuestion)}</div>\r\n                  </div>\r\n                )\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n      \r\n      {/* Render follow-ups without parent if parent is not visible but follow-ups are */}\r\n      {!isParentVisible && followUps.some(f => f.isVisible) && (\r\n        <div className=\"space-y-3\">\r\n          {followUps.map(({ question: followUpQuestion, isVisible: isFollowUpVisible }) => (\r\n            isFollowUpVisible && (\r\n              <div\r\n                key={followUpQuestion.id}\r\n                className=\"border border-gray-200 dark:border-gray-700 rounded-md p-4 bg-white dark:bg-gray-800\"\r\n              >\r\n                <div className=\"mb-2\">\r\n                  <Label className=\"text-base font-medium\">\r\n                    {followUpQuestion.label}\r\n                    {followUpQuestion.isRequired && (\r\n                      <span className=\"text-red-500 ml-1\">*</span>\r\n                    )}\r\n                  </Label>\r\n                  {followUpQuestion.hint && (\r\n                    <p className=\"text-sm text-muted-foreground mt-1\">\r\n                      {followUpQuestion.hint}\r\n                    </p>\r\n                  )}\r\n                  {errors[followUpQuestion.id] && (\r\n                    <p className=\"text-sm text-red-500 mt-1\">\r\n                      {errors[followUpQuestion.id]}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n                <div className=\"mt-2\">{renderQuestionInput(followUpQuestion)}</div>\r\n              </div>\r\n            )\r\n          ))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NestedQuestionRenderer;\r\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAqBA,MAAM,yBAAgE,CAAC,EACrE,aAAa,EACb,mBAAmB,EACnB,MAAM,EACN,YAAY,EAAE,EACf;IACC,MAAM,EAAE,UAAU,cAAc,EAAE,WAAW,eAAe,EAAE,SAAS,EAAE,GAAG;IAE5E,yEAAyE;IACzE,IAAI,CAAC,mBAAmB,CAAC,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,GAAG;QACzD,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,GAAG,WAAW;;YAE3B,iCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0HAAA,CAAA,QAAK;gCAAC,WAAU;;oCACd,eAAe,KAAK;oCACpB,eAAe,UAAU,kBACxB,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;4BAGvC,eAAe,IAAI,kBAClB,8OAAC;gCAAE,WAAU;0CACV,eAAe,IAAI;;;;;;4BAGvB,MAAM,CAAC,eAAe,EAAE,CAAC,kBACxB,8OAAC;gCAAE,WAAU;0CACV,MAAM,CAAC,eAAe,EAAE,CAAC;;;;;;;;;;;;kCAIhC,8OAAC;wBAAI,WAAU;kCAAQ,oBAAoB;;;;;;oBAG1C,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,mBAC9B,8OAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,EAAE,UAAU,gBAAgB,EAAE,WAAW,iBAAiB,EAAE,GAC1E,mCACE,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0HAAA,CAAA,QAAK;gDAAC,WAAU;;oDACd,iBAAiB,KAAK;oDACtB,iBAAiB,UAAU,kBAC1B,8OAAC;wDAAK,WAAU;kEAAoB;;;;;;;;;;;;4CAGvC,iBAAiB,IAAI,kBACpB,8OAAC;gDAAE,WAAU;0DACV,iBAAiB,IAAI;;;;;;4CAGzB,MAAM,CAAC,iBAAiB,EAAE,CAAC,kBAC1B,8OAAC;gDAAE,WAAU;0DACV,MAAM,CAAC,iBAAiB,EAAE,CAAC;;;;;;;;;;;;kDAIlC,8OAAC;wCAAI,WAAU;kDAAQ,oBAAoB;;;;;;;+BArBtC,iBAAiB,EAAE;;;;;;;;;;;;;;;;YA+BrC,CAAC,mBAAmB,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,mBAClD,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,EAAE,UAAU,gBAAgB,EAAE,WAAW,iBAAiB,EAAE,GAC1E,mCACE,8OAAC;wBAEC,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0HAAA,CAAA,QAAK;wCAAC,WAAU;;4CACd,iBAAiB,KAAK;4CACtB,iBAAiB,UAAU,kBAC1B,8OAAC;gDAAK,WAAU;0DAAoB;;;;;;;;;;;;oCAGvC,iBAAiB,IAAI,kBACpB,8OAAC;wCAAE,WAAU;kDACV,iBAAiB,IAAI;;;;;;oCAGzB,MAAM,CAAC,iBAAiB,EAAE,CAAC,kBAC1B,8OAAC;wCAAE,WAAU;kDACV,MAAM,CAAC,iBAAiB,EAAE,CAAC;;;;;;;;;;;;0CAIlC,8OAAC;gCAAI,WAAU;0CAAQ,oBAAoB;;;;;;;uBArBtC,iBAAiB,EAAE;;;;;;;;;;;;;;;;AA6BxC;uCAEe", "debugId": null}}, {"offset": {"line": 2242, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/form-preview/editForm.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from \"react\";\r\nimport { Question, QuestionGroup } from \"@/types/formBuilder\";\r\nimport { Submission as BaseSubmission } from \"@/app/(main)/project/[hashedId]/data/table/columns\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\r\nimport { ChevronDown, ChevronRight, ArrowRight } from \"lucide-react\";\r\nimport { useMutation, useQuery } from \"@tanstack/react-query\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { TableInput } from \"@/components/form-inputs/TableInput\";\r\nimport axios from \"@/lib/axios\";\r\nimport { updateMultipleAnswersWithEndpoint } from \"@/lib/api/submission\";\r\nimport { fetchQuestionGroups } from \"@/lib/api/question-groups\";\r\nimport { fetchProjectById } from \"@/lib/api/projects\";\r\nimport { Project } from \"@/types\";\r\nimport {\r\n  getVisibleQuestions,\r\n  cleanupHiddenAnswers,\r\n  restoreOriginalAnswers,\r\n  validateVisibleQuestions,\r\n  getNestedQuestions,\r\n} from \"@/lib/conditionalQuestions\";\r\nimport NestedQuestionRenderer from \"@/components/form-inputs/NestedQuestionRenderer\";\r\n\r\n// Extend Submission type to include id in answers\r\ninterface Submission extends BaseSubmission {\r\n  answers: Array<{\r\n    id: number; // Changed to non-optional\r\n    value: string | number;\r\n    question: {\r\n      inputType: string;\r\n      id: number;\r\n      label: string;\r\n      type?: string;\r\n    };\r\n  }>;\r\n}\r\n\r\ninterface EditFormProps {\r\n  questions: Question[];\r\n  submission: Submission;\r\n  projectId: number;\r\n  submissionId: number;\r\n  onClose?: () => void;\r\n  onSave: () => void;\r\n}\r\n\r\nexport function EditForm({\r\n  questions,\r\n  submission,\r\n  projectId,\r\n  submissionId,\r\n  onClose,\r\n  onSave,\r\n}: EditFormProps) {\r\n  const dispatch = useDispatch();\r\n  const [answers, setAnswers] = useState<Record<string, any>>({});\r\n  const [originalAnswers, setOriginalAnswers] = useState<Record<string, any>>({});\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [visibleQuestions, setVisibleQuestions] = useState<Question[]>([]);\r\n  const [nestedQuestions, setNestedQuestions] = useState<\r\n    Array<{\r\n      question: Question;\r\n      isVisible: boolean;\r\n      isFollowUp: boolean;\r\n      followUps: Array<{\r\n        question: Question;\r\n        isVisible: boolean;\r\n      }>;\r\n    }>\r\n  >([]);\r\n  const [expandedGroups, setExpandedGroups] = useState<Record<number, boolean>>(\r\n    {}\r\n  );\r\n\r\n  // Fetch question groups for the project\r\n  const { data: questionGroups = [] } = useQuery<QuestionGroup[]>({\r\n    queryKey: [\"questionGroups\", projectId],\r\n    queryFn: () => fetchQuestionGroups({ projectId }),\r\n    enabled: !!projectId,\r\n  });\r\n\r\n  // Fetch project data to get the project name\r\n  const { data: projectData } = useQuery<Project>({\r\n    queryKey: [\"project\", projectId],\r\n    queryFn: () => fetchProjectById({ projectId }),\r\n    enabled: !!projectId,\r\n  });\r\n\r\n  // Initialize answers from submission\r\n  useEffect(() => {\r\n    const initialAnswers: Record<string, any> = {};\r\n    questions.forEach((question) => {\r\n      if (question.inputType === \"selectmany\") {\r\n        const selectManyAnswers = submission.answers.filter(\r\n          (a) => a.question.id === question.id\r\n        );\r\n        initialAnswers[question.id] = selectManyAnswers\r\n          .map((answer: { value: string | number }) => answer.value)\r\n          .filter(\r\n            (value: string | number) =>\r\n              value != null && String(value).trim() !== \"\"\r\n          );\r\n      } else {\r\n        const answer = submission.answers.find(\r\n          (a) => a.question.id === question.id\r\n        );\r\n        initialAnswers[question.id] = answer?.value ?? \"\";\r\n      }\r\n    });\r\n    setAnswers(initialAnswers);\r\n    // Store a deep copy of the original answers for persistence\r\n    setOriginalAnswers(JSON.parse(JSON.stringify(initialAnswers)));\r\n  }, [questions, submission]);\r\n\r\n  // Update visible questions when answers or questions change\r\n  useEffect(() => {\r\n    if (questions && Object.keys(originalAnswers).length > 0) {\r\n      const newVisibleQuestions = getVisibleQuestions(questions, answers);\r\n      setVisibleQuestions(newVisibleQuestions);\r\n\r\n      // Calculate nested question structure\r\n      const newNestedQuestions = getNestedQuestions(questions, answers);\r\n      setNestedQuestions(newNestedQuestions);\r\n\r\n      // First, restore any original answers for questions that have become visible again\r\n      const restoredAnswers = restoreOriginalAnswers(answers, newVisibleQuestions, originalAnswers);\r\n\r\n      // Then clean up answers for questions that are no longer visible\r\n      const cleanedAnswers = cleanupHiddenAnswers(restoredAnswers, newVisibleQuestions);\r\n\r\n      // Only update if there are actual changes\r\n      const hasChanges = Object.keys(cleanedAnswers).length !== Object.keys(answers).length ||\r\n                        Object.keys(restoredAnswers).some(key => restoredAnswers[key] !== answers[key]);\r\n\r\n      if (hasChanges) {\r\n        setAnswers(cleanedAnswers);\r\n      }\r\n    }\r\n  }, [questions, answers, originalAnswers]);\r\n\r\n  // Initialize all groups as expanded when questionGroups change\r\n  useEffect(() => {\r\n    if (questionGroups.length > 0) {\r\n      const initialExpandedState: Record<number, boolean> = {};\r\n      questionGroups.forEach((group) => {\r\n        initialExpandedState[group.id] = true;\r\n      });\r\n      setExpandedGroups(initialExpandedState);\r\n    }\r\n  }, [questionGroups.length]); // Only depend on length to avoid infinite loops\r\n\r\n  // Group questions by their group ID - memoized to prevent recalculation\r\n  const groupedQuestions = useMemo(() => {\r\n    return questionGroups.reduce(\r\n      (acc: Record<number, Question[]>, group: QuestionGroup) => {\r\n        acc[group.id] = questions.filter((q) => q.questionGroupId === group.id);\r\n        return acc;\r\n      },\r\n      {} as Record<number, Question[]>\r\n    );\r\n  }, [questionGroups, questions]);\r\n\r\n  // Get ungrouped questions - memoized to prevent recalculation\r\n  const ungroupedQuestions = useMemo(() => {\r\n    return questions.filter(\r\n      (q) => q.questionGroupId === null || q.questionGroupId === undefined\r\n    );\r\n  }, [questions]);\r\n\r\n  // Create a unified list of form items (groups and individual questions) for dynamic ordering\r\n  const unifiedFormItems = useMemo(() => {\r\n    const items: Array<{\r\n      type: 'group' | 'question';\r\n      data: QuestionGroup | Question;\r\n      order: number;\r\n      originalPosition?: number;\r\n    }> = [];\r\n\r\n    // Add question groups\r\n    questionGroups.forEach((group: QuestionGroup) => {\r\n      // For groups, find the minimum position of questions in the group\r\n      const groupQuestions = questions.filter(q => q.questionGroupId === group.id);\r\n      const minQuestionPosition = groupQuestions.length > 0\r\n        ? Math.min(...groupQuestions.map(q => q.position))\r\n        : group.order;\r\n\r\n      items.push({\r\n        type: 'group',\r\n        data: group,\r\n        order: minQuestionPosition,\r\n        originalPosition: minQuestionPosition\r\n      });\r\n    });\r\n\r\n    // Add ungrouped questions\r\n    ungroupedQuestions.forEach((question: Question) => {\r\n      items.push({\r\n        type: 'question',\r\n        data: question,\r\n        order: question.position,\r\n        originalPosition: question.position\r\n      });\r\n    });\r\n\r\n    // Sort by order/position with secondary sort for consistency\r\n    return items.sort((a, b) => {\r\n      if (a.order === b.order) {\r\n        return (a.originalPosition || a.order) - (b.originalPosition || b.order);\r\n      }\r\n      return a.order - b.order;\r\n    });\r\n  }, [questionGroups, ungroupedQuestions, questions]);\r\n\r\n  // Toggle group expansion - memoized to prevent unnecessary re-renders\r\n  const toggleGroupExpansion = useCallback((groupId: number) => {\r\n    setExpandedGroups((prev) => ({\r\n      ...prev,\r\n      [groupId]: !prev[groupId],\r\n    }));\r\n  }, []);\r\n\r\n  const handleInputChange = useCallback((questionId: number, value: any) => {\r\n    setAnswers((prev) => ({\r\n      ...prev,\r\n      [questionId]: value,\r\n    }));\r\n    setErrors((prev) => ({\r\n      ...prev,\r\n      [questionId]: \"\",\r\n    }));\r\n  }, []);\r\n\r\n  const validateForm = () => {\r\n    // Only validate visible questions\r\n    const newErrors = validateVisibleQuestions(visibleQuestions, answers);\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const updateAnswersMutation = useMutation({\r\n    mutationFn: async (answers: Record<string, any>) => {\r\n      const formattedAnswers = questions.map((question) => {\r\n        const answerValue = answers[question.id];\r\n        const isSelectMany = question.inputType === \"selectmany\";\r\n        const submissionAnswer = submission.answers.find(\r\n          (sa) => sa.question.id === question.id\r\n        );\r\n\r\n        // For existing answers, use the answer ID\r\n        // For new questions, we'll create a new answer\r\n        const isNewQuestion = !submissionAnswer?.id;\r\n\r\n        if (isNewQuestion) {\r\n        }\r\n\r\n        if (isSelectMany && Array.isArray(answerValue)) {\r\n          if (answerValue.length > 0) {\r\n            let questionOptionIds: number[] = [];\r\n            if (question.questionOptions) {\r\n              questionOptionIds = answerValue\r\n                .map((val: string) => {\r\n                  const option = question.questionOptions.find(\r\n                    (opt) => opt.label === val\r\n                  );\r\n                  return option?.id;\r\n                })\r\n                .filter((id): id is number => id !== undefined);\r\n            }\r\n\r\n            const baseAnswer = {\r\n              projectId,\r\n              questionId: question.id, // Always include questionId\r\n              answerType: question.inputType,\r\n              value: answerValue.join(\", \"),\r\n              questionOptionId: questionOptionIds,\r\n              isOtherOption: false,\r\n              formSubmissionId: submissionId,\r\n            };\r\n\r\n            // If it's a new question, just use the base answer\r\n            // If it's an existing answer, add the id\r\n            if (isNewQuestion) {\r\n              return baseAnswer;\r\n            } else {\r\n              return {\r\n                ...baseAnswer,\r\n                id: submissionAnswer.id,\r\n              };\r\n            }\r\n          }\r\n          return null;\r\n        } else {\r\n          let formattedValue: string | number | boolean | undefined;\r\n          if (\r\n            question.inputType === \"number\" ||\r\n            question.inputType === \"decimal\"\r\n          ) {\r\n            formattedValue = answerValue ? Number(answerValue) : undefined;\r\n          } else if (\r\n            question.inputType === \"date\" ||\r\n            question.inputType === \"dateandtime\"\r\n          ) {\r\n            formattedValue = answerValue || undefined;\r\n          } else if (question.inputType === \"table\") {\r\n            // For table input type, convert the array of cell values to JSON string\r\n            formattedValue =\r\n              Array.isArray(answerValue) && answerValue.length > 0\r\n                ? JSON.stringify(answerValue)\r\n                : undefined;\r\n          } else {\r\n            formattedValue = answerValue ? String(answerValue) : undefined;\r\n          }\r\n\r\n          if (formattedValue === undefined) {\r\n            return null;\r\n          }\r\n\r\n          let questionOptionId: number | undefined;\r\n          if (\r\n            question.inputType === \"selectone\" &&\r\n            answerValue &&\r\n            question.questionOptions\r\n          ) {\r\n            const option = question.questionOptions.find(\r\n              (opt) => opt.label === answerValue\r\n            );\r\n            questionOptionId = option?.id;\r\n          }\r\n\r\n          const baseAnswer = {\r\n            projectId,\r\n            questionId: question.id, // Always include questionId\r\n            answerType: question.inputType,\r\n            value: formattedValue,\r\n            questionOptionId,\r\n            isOtherOption: false,\r\n            formSubmissionId: submissionId,\r\n          };\r\n\r\n          // If it's a new question, just use the base answer\r\n          // If it's an existing answer, add the id\r\n          if (isNewQuestion) {\r\n            return baseAnswer;\r\n          } else {\r\n            return {\r\n              ...baseAnswer,\r\n              id: submissionAnswer.id,\r\n            };\r\n          }\r\n        }\r\n      });\r\n\r\n      // Filter out null values\r\n      const validAnswers = formattedAnswers.filter(\r\n        (answer) => answer !== null\r\n      ) as any[];\r\n\r\n      if (validAnswers.length === 0) {\r\n        throw new Error(\"No valid answers with IDs to submit\");\r\n      }\r\n\r\n      const simplifiedAnswers = validAnswers\r\n        .map((answer) => {\r\n          // For existing answers (with id)\r\n          if (answer.id) {\r\n            return {\r\n              id: answer.id,\r\n              questionId: answer.questionId, // Include questionId for all answers\r\n              projectId,\r\n              value: answer.value,\r\n              answerType: answer.answerType,\r\n              questionOptionId: answer.questionOptionId,\r\n              isOtherOption: answer.isOtherOption || false,\r\n              formSubmissionId: answer.formSubmissionId,\r\n            };\r\n          }\r\n          // For new questions (with questionId)\r\n          else if (answer.questionId) {\r\n            return {\r\n              questionId: answer.questionId,\r\n              projectId,\r\n              value: answer.value,\r\n              answerType: answer.answerType,\r\n              questionOptionId: answer.questionOptionId,\r\n              isOtherOption: answer.isOtherOption || false,\r\n              formSubmissionId: answer.formSubmissionId,\r\n            };\r\n          }\r\n          return null;\r\n        })\r\n        .filter((answer) => answer !== null);\r\n\r\n      try {\r\n        // Cast the array to any to bypass TypeScript's strict checking\r\n        // The backend can handle both formats (with id or with questionId)\r\n        const data = await updateMultipleAnswersWithEndpoint(\r\n          simplifiedAnswers as any,\r\n          projectId\r\n        );\r\n\r\n        return data;\r\n      } catch (error: any) {\r\n        console.error(\"Error with /answers/multiple endpoint:\", error);\r\n        if (error.response) {\r\n          console.error(\r\n            \"Error response data:\",\r\n            JSON.stringify(error.response.data, null, 2)\r\n          );\r\n          console.error(\"Error response status:\", error.response.status);\r\n          console.error(\"Error response headers:\", error.response.headers);\r\n        }\r\n\r\n        const results = [];\r\n        const failedQuestions: number[] = [];\r\n\r\n        for (const answer of validAnswers) {\r\n          try {\r\n            // For existing answers with ID, use PATCH\r\n            if (answer.id) {\r\n              const { data } = await axios.patch(\r\n                `/answers/${answer.id}?projectId=${projectId}`,\r\n                {\r\n                  id: answer.id,\r\n                  questionId: answer.questionId, // Include questionId for all answers\r\n                  projectId,\r\n                  value: answer.value,\r\n                  answerType: answer.answerType,\r\n                  questionOptionId: answer.questionOptionId,\r\n                  isOtherOption: answer.isOtherOption || false,\r\n                  formSubmissionId: answer.formSubmissionId,\r\n                }\r\n              );\r\n              results.push(data);\r\n            }\r\n            // For new questions, use POST to create a new answer\r\n            else if (answer.questionId) {\r\n              const { data } = await axios.post(\r\n                `/answers?projectId=${projectId}`,\r\n                {\r\n                  submissionId: answer.formSubmissionId,\r\n                  questionId: answer.questionId,\r\n                  value: answer.value,\r\n                  answerType: answer.answerType,\r\n                  questionOptionId: answer.questionOptionId,\r\n                  isOtherOption: answer.isOtherOption || false,\r\n                }\r\n              );\r\n              results.push(data);\r\n            }\r\n          } catch (individualError: any) {\r\n            const identifier = answer.id || answer.questionId;\r\n            console.error(\r\n              `Error handling answer ${identifier}:`,\r\n              individualError\r\n            );\r\n            if (individualError.response) {\r\n              console.error(\r\n                \"Individual error response data:\",\r\n                JSON.stringify(individualError.response.data, null, 2)\r\n              );\r\n            }\r\n            failedQuestions.push(identifier);\r\n          }\r\n        }\r\n\r\n        if (failedQuestions.length > 0) {\r\n          throw new Error(\r\n            `Failed to update answers with IDs: ${failedQuestions.join(\", \")}`\r\n          );\r\n        }\r\n\r\n        if (results.length > 0) {\r\n          dispatch(\r\n            showNotification({\r\n              message:\r\n                \"Submission updated successfully using individual updates. Consider checking the bulk update endpoint.\",\r\n              type: \"warning\",\r\n            })\r\n          );\r\n          return results;\r\n        }\r\n\r\n        throw error;\r\n      }\r\n    },\r\n    onSuccess: () => {\r\n      dispatch(\r\n        showNotification({\r\n          message:\r\n            \"Submission updated successfully. You can continue editing if needed.\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n      onSave();\r\n    },\r\n    onError: (error: any) => {\r\n      const errorMessage =\r\n        error.response?.data?.message ||\r\n        error.response?.data?.error ||\r\n        error.message ||\r\n        \"Failed to update submission. Please check your input and try again.\";\r\n      dispatch(\r\n        showNotification({\r\n          message: errorMessage,\r\n          type: \"error\",\r\n        })\r\n      );\r\n      console.error(\"Update Error:\", {\r\n        message: errorMessage,\r\n        status: error.response?.status,\r\n        data: JSON.stringify(error.response?.data, null, 2),\r\n      });\r\n    },\r\n    onSettled: () => {\r\n      setIsSubmitting(false);\r\n    },\r\n  });\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!validateForm()) return;\r\n    setIsSubmitting(true);\r\n    updateAnswersMutation.mutate(answers);\r\n  };\r\n\r\n  // Helper function to check if a question is a follow-up question\r\n  const isFollowUpQuestion = (questionId: number): boolean => {\r\n    return questions.some((q) =>\r\n      q.questionOptions?.some((option) => option.nextQuestionId === questionId)\r\n    );\r\n  };\r\n\r\n  // Helper function to check if a question has follow-up questions\r\n  const hasFollowUpQuestions = (question: Question): boolean => {\r\n    return question.questionOptions?.some((option) => option.nextQuestionId) || false;\r\n  };\r\n\r\n  // Render a single question with its input and visual indicators\r\n  const renderQuestion = (question: Question) => {\r\n    const isFollowUp = isFollowUpQuestion(question.id);\r\n    const hasFollowUps = hasFollowUpQuestions(question);\r\n\r\n    return (\r\n      <div\r\n        key={question.id}\r\n        className={`border rounded-md p-4 ${\r\n          isFollowUp\r\n            ? \"border-primary-200 dark:border-primary-700 bg-primary-100 dark:bg-primary-900/20\"\r\n            : \"border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\"\r\n        }`}\r\n      >\r\n        <div className=\"mb-2\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <Label className=\"text-base font-medium\">\r\n              {question.label}\r\n              {question.isRequired && (\r\n                <span className=\"text-red-500 ml-1\">*</span>\r\n              )}\r\n            </Label>\r\n            {/* Visual indicators */}\r\n            {isFollowUp && (\r\n              <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200\">\r\n                <ArrowRight className=\"w-3 h-3 mr-1\" />\r\n                Follow-up\r\n              </span>\r\n            )}\r\n            {hasFollowUps && (\r\n              <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-200 text-accent-700 dark:bg-accent-700/20 dark:text-accent-200\">\r\n                Has conditions\r\n              </span>\r\n            )}\r\n          </div>\r\n          {question.hint && (\r\n            <p className={`text-sm mt-1 ${\r\n              isFollowUp\r\n                ? \"text-primary-700 dark:text-primary-300\"\r\n                : \"text-muted-foreground\"\r\n            }`}>\r\n              {question.hint}\r\n            </p>\r\n          )}\r\n          {errors[question.id] && (\r\n            <p className=\"text-sm text-red-500 mt-1\">\r\n              {errors[question.id]}\r\n            </p>\r\n          )}\r\n        </div>\r\n        <div className=\"mt-2\">{renderQuestionInput(question)}</div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const renderQuestionInput = (question: Question) => {\r\n    const value =\r\n      answers[question.id] ?? (question.inputType === \"selectmany\" ? [] : \"\");\r\n\r\n    switch (question.inputType) {\r\n      case \"text\":\r\n        if (question.hint?.includes(\"multiline\")) {\r\n          return (\r\n            <Textarea\r\n              value={value}\r\n              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>\r\n                handleInputChange(question.id, e.target.value)\r\n              }\r\n              placeholder={question.hint || \"Your answer\"}\r\n              required={question.isRequired}\r\n            />\r\n          );\r\n        }\r\n        return (\r\n          <input\r\n            className=\"input-field w-full\"\r\n            value={value}\r\n            onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n            placeholder={question.hint || \"Your answer\"}\r\n            required={question.isRequired}\r\n          />\r\n        );\r\n\r\n      case \"number\":\r\n        return (\r\n          <input\r\n            className=\"input-field w-full\"\r\n            type=\"number\"\r\n            value={value}\r\n            onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n            placeholder={question.hint || \"Your answer\"}\r\n            required={question.isRequired}\r\n          />\r\n        );\r\n\r\n      case \"decimal\":\r\n        return (\r\n          <input\r\n            className=\"input-field w-full\"\r\n            type=\"number\"\r\n            step=\"any\"\r\n            value={value}\r\n            onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n            placeholder={question.hint || \"Your answer\"}\r\n            required={question.isRequired}\r\n          />\r\n        );\r\n\r\n      case \"selectone\":\r\n        return (\r\n          <RadioGroup\r\n            value={value}\r\n            onValueChange={(val: string) => handleInputChange(question.id, val)}\r\n            required={question.isRequired}\r\n          >\r\n            <div className=\"space-y-2\">\r\n              {question.questionOptions?.map((option, index) => (\r\n                <div key={index} className=\"flex items-center space-x-2\">\r\n                  <RadioGroupItem\r\n                    value={option.label}\r\n                    id={`option-${option.id}`}\r\n                  />\r\n                  <Label\r\n                    htmlFor={`option-${option.id}`}\r\n                    className=\"cursor-pointer\"\r\n                  >\r\n                    {option.label}\r\n                  </Label>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </RadioGroup>\r\n        );\r\n\r\n      case \"selectmany\":\r\n        return (\r\n          <div className=\"space-y-2\">\r\n            {question.questionOptions?.map((option) => (\r\n              <div key={option.id} className=\"flex items-center space-x-2\">\r\n                <Checkbox\r\n                  id={`option-${option.id}`}\r\n                  checked={(value || []).includes(option.label)}\r\n                  onCheckedChange={(checked) => {\r\n                    const currentValues = value || [];\r\n                    const newValues = checked\r\n                      ? [...currentValues, option.label]\r\n                      : currentValues.filter((v: string) => v !== option.label);\r\n                    handleInputChange(question.id, newValues);\r\n                  }}\r\n                />\r\n                <Label\r\n                  htmlFor={`option-${option.id}`}\r\n                  className=\"cursor-pointer\"\r\n                >\r\n                  {option.label}\r\n                </Label>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        );\r\n\r\n      case \"date\":\r\n        return (\r\n          <div className=\"relative\">\r\n            <input\r\n              className=\"input-field w-full\"\r\n              type=\"date\"\r\n              value={value}\r\n              onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n              placeholder={question.hint || \"Select date\"}\r\n              required={question.isRequired}\r\n            />\r\n          </div>\r\n        );\r\n\r\n      case \"dateandtime\":\r\n        return (\r\n          <div className=\"relative\">\r\n            <input\r\n              className=\"input-field w-full\"\r\n              type=\"time\"\r\n              value={value}\r\n              onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n              placeholder={question.hint || \"Select time\"}\r\n              required={question.isRequired}\r\n            />\r\n          </div>\r\n        );\r\n\r\n      case \"table\":\r\n        return (\r\n          <TableInput\r\n            questionId={question.id}\r\n            value={value}\r\n            onChange={(cellValues) =>\r\n              handleInputChange(question.id, cellValues)\r\n            }\r\n            required={question.isRequired}\r\n            tableLabel={question.label}\r\n          />\r\n        );\r\n\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700\">\r\n      <h2 className=\"text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700\">\r\n        Edit Submission{projectData?.name ? ` for ${projectData.name}` : \"\"}\r\n      </h2>\r\n      <form onSubmit={handleSubmit} className=\"p-6\">\r\n        <div className=\"space-y-6\">\r\n          {questions.length === 0 ? (\r\n            <div className=\"text-center py-12\">\r\n              <p className=\"text-muted-foreground\">This form has no questions yet.</p>\r\n            </div>\r\n          ) : (\r\n            // Render unified form items (groups and individual questions) in order\r\n            unifiedFormItems.map((item) => {\r\n              if (item.type === 'group') {\r\n                const group = item.data as QuestionGroup;\r\n                const groupQuestions = groupedQuestions[group.id] || [];\r\n                const visibleGroupQuestions = groupQuestions.filter((q) =>\r\n                  visibleQuestions.some((vq) => vq.id === q.id)\r\n                );\r\n                const isExpanded = expandedGroups[group.id];\r\n\r\n                if (visibleGroupQuestions.length === 0) return null;\r\n\r\n                return (\r\n                  <div\r\n                    key={`group-${group.id}`}\r\n                    className=\"border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800\"\r\n                  >\r\n                    {/* Group Header */}\r\n                    <div\r\n                      className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\"\r\n                      onClick={() => toggleGroupExpansion(group.id)}\r\n                    >\r\n                      <div className=\"flex items-center space-x-2\">\r\n                        {isExpanded ? (\r\n                          <ChevronDown className=\"h-5 w-5 text-gray-500\" />\r\n                        ) : (\r\n                          <ChevronRight className=\"h-5 w-5 text-gray-500\" />\r\n                        )}\r\n                        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n                          {group.title}\r\n                        </h3>\r\n                        <span className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                          ({visibleGroupQuestions.length} visible question\r\n                          {visibleGroupQuestions.length !== 1 ? \"s\" : \"\"})\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Group Content with Nested Questions */}\r\n                    {isExpanded && (\r\n                      <div className=\"p-4 space-y-4\">\r\n                        {nestedQuestions\r\n                          .filter((nq) =>\r\n                            groupQuestions.some(\r\n                              (gq) => gq.id === nq.question.id\r\n                            )\r\n                          )\r\n                          .map((questionGroup) => (\r\n                            <NestedQuestionRenderer\r\n                              key={questionGroup.question.id}\r\n                              questionGroup={questionGroup}\r\n                              renderQuestionInput={renderQuestionInput}\r\n                              errors={errors}\r\n                              className=\"\"\r\n                            />\r\n                          ))}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                );\r\n              } else {\r\n                const question = item.data as Question;\r\n                // Only render ungrouped questions that are visible\r\n                if (!visibleQuestions.some((vq) => vq.id === question.id)) {\r\n                  return null;\r\n                }\r\n\r\n                // Find the nested question structure for this question\r\n                const nestedQuestion = nestedQuestions.find(\r\n                  (nq) => nq.question.id === question.id\r\n                );\r\n\r\n                if (nestedQuestion) {\r\n                  return (\r\n                    <NestedQuestionRenderer\r\n                      key={question.id}\r\n                      questionGroup={nestedQuestion}\r\n                      renderQuestionInput={renderQuestionInput}\r\n                      errors={errors}\r\n                      className=\"\"\r\n                    />\r\n                  );\r\n                }\r\n\r\n                return renderQuestion(question);\r\n              }\r\n            })\r\n          )}\r\n\r\n          {questions.length > 0 && (\r\n            <div className=\"mt-6 flex justify-end gap-4\">\r\n              <button\r\n                className=\"btn-primary bg-neutral-500 hover:bg-neutral-600\"\r\n                type=\"button\"\r\n                onClick={onClose}\r\n                disabled={isSubmitting}\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                className=\"btn-primary\"\r\n                type=\"submit\"\r\n                disabled={isSubmitting}\r\n              >\r\n                {isSubmitting ? \"Saving...\" : \"Save Changes\"}\r\n              </button>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAOA;AA1BA;;;;;;;;;;;;;;;;;;AAmDO,SAAS,SAAS,EACvB,SAAS,EACT,UAAU,EACV,SAAS,EACT,YAAY,EACZ,OAAO,EACP,MAAM,EACQ;IACd,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IAC7E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACvE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAUnD,EAAE;IACJ,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACjD,CAAC;IAGH,wCAAwC;IACxC,MAAM,EAAE,MAAM,iBAAiB,EAAE,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAmB;QAC9D,UAAU;YAAC;YAAkB;SAAU;QACvC,SAAS,IAAM,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE;gBAAE;YAAU;QAC/C,SAAS,CAAC,CAAC;IACb;IAEA,6CAA6C;IAC7C,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAW;QAC9C,UAAU;YAAC;YAAW;SAAU;QAChC,SAAS,IAAM,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;gBAAE;YAAU;QAC5C,SAAS,CAAC,CAAC;IACb;IAEA,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAsC,CAAC;QAC7C,UAAU,OAAO,CAAC,CAAC;YACjB,IAAI,SAAS,SAAS,KAAK,cAAc;gBACvC,MAAM,oBAAoB,WAAW,OAAO,CAAC,MAAM,CACjD,CAAC,IAAM,EAAE,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE;gBAEtC,cAAc,CAAC,SAAS,EAAE,CAAC,GAAG,kBAC3B,GAAG,CAAC,CAAC,SAAuC,OAAO,KAAK,EACxD,MAAM,CACL,CAAC,QACC,SAAS,QAAQ,OAAO,OAAO,IAAI,OAAO;YAElD,OAAO;gBACL,MAAM,SAAS,WAAW,OAAO,CAAC,IAAI,CACpC,CAAC,IAAM,EAAE,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE;gBAEtC,cAAc,CAAC,SAAS,EAAE,CAAC,GAAG,QAAQ,SAAS;YACjD;QACF;QACA,WAAW;QACX,4DAA4D;QAC5D,mBAAmB,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;IAC/C,GAAG;QAAC;QAAW;KAAW;IAE1B,4DAA4D;IAC5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,OAAO,IAAI,CAAC,iBAAiB,MAAM,GAAG,GAAG;YACxD,MAAM,sBAAsB,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW;YAC3D,oBAAoB;YAEpB,sCAAsC;YACtC,MAAM,qBAAqB,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW;YACzD,mBAAmB;YAEnB,mFAAmF;YACnF,MAAM,kBAAkB,CAAA,GAAA,2HAAA,CAAA,yBAAsB,AAAD,EAAE,SAAS,qBAAqB;YAE7E,iEAAiE;YACjE,MAAM,iBAAiB,CAAA,GAAA,2HAAA,CAAA,uBAAoB,AAAD,EAAE,iBAAiB;YAE7D,0CAA0C;YAC1C,MAAM,aAAa,OAAO,IAAI,CAAC,gBAAgB,MAAM,KAAK,OAAO,IAAI,CAAC,SAAS,MAAM,IACnE,OAAO,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAA,MAAO,eAAe,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI;YAEhG,IAAI,YAAY;gBACd,WAAW;YACb;QACF;IACF,GAAG;QAAC;QAAW;QAAS;KAAgB;IAExC,+DAA+D;IAC/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,MAAM,uBAAgD,CAAC;YACvD,eAAe,OAAO,CAAC,CAAC;gBACtB,oBAAoB,CAAC,MAAM,EAAE,CAAC,GAAG;YACnC;YACA,kBAAkB;QACpB;IACF,GAAG;QAAC,eAAe,MAAM;KAAC,GAAG,gDAAgD;IAE7E,wEAAwE;IACxE,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,OAAO,eAAe,MAAM,CAC1B,CAAC,KAAiC;YAChC,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,UAAU,MAAM,CAAC,CAAC,IAAM,EAAE,eAAe,KAAK,MAAM,EAAE;YACtE,OAAO;QACT,GACA,CAAC;IAEL,GAAG;QAAC;QAAgB;KAAU;IAE9B,8DAA8D;IAC9D,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACjC,OAAO,UAAU,MAAM,CACrB,CAAC,IAAM,EAAE,eAAe,KAAK,QAAQ,EAAE,eAAe,KAAK;IAE/D,GAAG;QAAC;KAAU;IAEd,6FAA6F;IAC7F,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,MAAM,QAKD,EAAE;QAEP,sBAAsB;QACtB,eAAe,OAAO,CAAC,CAAC;YACtB,kEAAkE;YAClE,MAAM,iBAAiB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,KAAK,MAAM,EAAE;YAC3E,MAAM,sBAAsB,eAAe,MAAM,GAAG,IAChD,KAAK,GAAG,IAAI,eAAe,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,KAC9C,MAAM,KAAK;YAEf,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,kBAAkB;YACpB;QACF;QAEA,0BAA0B;QAC1B,mBAAmB,OAAO,CAAC,CAAC;YAC1B,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,MAAM;gBACN,OAAO,SAAS,QAAQ;gBACxB,kBAAkB,SAAS,QAAQ;YACrC;QACF;QAEA,6DAA6D;QAC7D,OAAO,MAAM,IAAI,CAAC,CAAC,GAAG;YACpB,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,EAAE;gBACvB,OAAO,CAAC,EAAE,gBAAgB,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,gBAAgB,IAAI,EAAE,KAAK;YACzE;YACA,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;QAC1B;IACF,GAAG;QAAC;QAAgB;QAAoB;KAAU;IAElD,sEAAsE;IACtE,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,kBAAkB,CAAC,OAAS,CAAC;gBAC3B,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ;YAC3B,CAAC;IACH,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,YAAoB;QACzD,WAAW,CAAC,OAAS,CAAC;gBACpB,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;QACD,UAAU,CAAC,OAAS,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;IACH,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,kCAAkC;QAClC,MAAM,YAAY,CAAA,GAAA,2HAAA,CAAA,2BAAwB,AAAD,EAAE,kBAAkB;QAC7D,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,wBAAwB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACxC,YAAY,OAAO;YACjB,MAAM,mBAAmB,UAAU,GAAG,CAAC,CAAC;gBACtC,MAAM,cAAc,OAAO,CAAC,SAAS,EAAE,CAAC;gBACxC,MAAM,eAAe,SAAS,SAAS,KAAK;gBAC5C,MAAM,mBAAmB,WAAW,OAAO,CAAC,IAAI,CAC9C,CAAC,KAAO,GAAG,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE;gBAGxC,0CAA0C;gBAC1C,+CAA+C;gBAC/C,MAAM,gBAAgB,CAAC,kBAAkB;gBAEzC,IAAI,eAAe,CACnB;gBAEA,IAAI,gBAAgB,MAAM,OAAO,CAAC,cAAc;oBAC9C,IAAI,YAAY,MAAM,GAAG,GAAG;wBAC1B,IAAI,oBAA8B,EAAE;wBACpC,IAAI,SAAS,eAAe,EAAE;4BAC5B,oBAAoB,YACjB,GAAG,CAAC,CAAC;gCACJ,MAAM,SAAS,SAAS,eAAe,CAAC,IAAI,CAC1C,CAAC,MAAQ,IAAI,KAAK,KAAK;gCAEzB,OAAO,QAAQ;4BACjB,GACC,MAAM,CAAC,CAAC,KAAqB,OAAO;wBACzC;wBAEA,MAAM,aAAa;4BACjB;4BACA,YAAY,SAAS,EAAE;4BACvB,YAAY,SAAS,SAAS;4BAC9B,OAAO,YAAY,IAAI,CAAC;4BACxB,kBAAkB;4BAClB,eAAe;4BACf,kBAAkB;wBACpB;wBAEA,mDAAmD;wBACnD,yCAAyC;wBACzC,IAAI,eAAe;4BACjB,OAAO;wBACT,OAAO;4BACL,OAAO;gCACL,GAAG,UAAU;gCACb,IAAI,iBAAiB,EAAE;4BACzB;wBACF;oBACF;oBACA,OAAO;gBACT,OAAO;oBACL,IAAI;oBACJ,IACE,SAAS,SAAS,KAAK,YACvB,SAAS,SAAS,KAAK,WACvB;wBACA,iBAAiB,cAAc,OAAO,eAAe;oBACvD,OAAO,IACL,SAAS,SAAS,KAAK,UACvB,SAAS,SAAS,KAAK,eACvB;wBACA,iBAAiB,eAAe;oBAClC,OAAO,IAAI,SAAS,SAAS,KAAK,SAAS;wBACzC,wEAAwE;wBACxE,iBACE,MAAM,OAAO,CAAC,gBAAgB,YAAY,MAAM,GAAG,IAC/C,KAAK,SAAS,CAAC,eACf;oBACR,OAAO;wBACL,iBAAiB,cAAc,OAAO,eAAe;oBACvD;oBAEA,IAAI,mBAAmB,WAAW;wBAChC,OAAO;oBACT;oBAEA,IAAI;oBACJ,IACE,SAAS,SAAS,KAAK,eACvB,eACA,SAAS,eAAe,EACxB;wBACA,MAAM,SAAS,SAAS,eAAe,CAAC,IAAI,CAC1C,CAAC,MAAQ,IAAI,KAAK,KAAK;wBAEzB,mBAAmB,QAAQ;oBAC7B;oBAEA,MAAM,aAAa;wBACjB;wBACA,YAAY,SAAS,EAAE;wBACvB,YAAY,SAAS,SAAS;wBAC9B,OAAO;wBACP;wBACA,eAAe;wBACf,kBAAkB;oBACpB;oBAEA,mDAAmD;oBACnD,yCAAyC;oBACzC,IAAI,eAAe;wBACjB,OAAO;oBACT,OAAO;wBACL,OAAO;4BACL,GAAG,UAAU;4BACb,IAAI,iBAAiB,EAAE;wBACzB;oBACF;gBACF;YACF;YAEA,yBAAyB;YACzB,MAAM,eAAe,iBAAiB,MAAM,CAC1C,CAAC,SAAW,WAAW;YAGzB,IAAI,aAAa,MAAM,KAAK,GAAG;gBAC7B,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,oBAAoB,aACvB,GAAG,CAAC,CAAC;gBACJ,iCAAiC;gBACjC,IAAI,OAAO,EAAE,EAAE;oBACb,OAAO;wBACL,IAAI,OAAO,EAAE;wBACb,YAAY,OAAO,UAAU;wBAC7B;wBACA,OAAO,OAAO,KAAK;wBACnB,YAAY,OAAO,UAAU;wBAC7B,kBAAkB,OAAO,gBAAgB;wBACzC,eAAe,OAAO,aAAa,IAAI;wBACvC,kBAAkB,OAAO,gBAAgB;oBAC3C;gBACF,OAEK,IAAI,OAAO,UAAU,EAAE;oBAC1B,OAAO;wBACL,YAAY,OAAO,UAAU;wBAC7B;wBACA,OAAO,OAAO,KAAK;wBACnB,YAAY,OAAO,UAAU;wBAC7B,kBAAkB,OAAO,gBAAgB;wBACzC,eAAe,OAAO,aAAa,IAAI;wBACvC,kBAAkB,OAAO,gBAAgB;oBAC3C;gBACF;gBACA,OAAO;YACT,GACC,MAAM,CAAC,CAAC,SAAW,WAAW;YAEjC,IAAI;gBACF,+DAA+D;gBAC/D,mEAAmE;gBACnE,MAAM,OAAO,MAAM,CAAA,GAAA,wHAAA,CAAA,oCAAiC,AAAD,EACjD,mBACA;gBAGF,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,IAAI,MAAM,QAAQ,EAAE;oBAClB,QAAQ,KAAK,CACX,wBACA,KAAK,SAAS,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE,MAAM;oBAE5C,QAAQ,KAAK,CAAC,0BAA0B,MAAM,QAAQ,CAAC,MAAM;oBAC7D,QAAQ,KAAK,CAAC,2BAA2B,MAAM,QAAQ,CAAC,OAAO;gBACjE;gBAEA,MAAM,UAAU,EAAE;gBAClB,MAAM,kBAA4B,EAAE;gBAEpC,KAAK,MAAM,UAAU,aAAc;oBACjC,IAAI;wBACF,0CAA0C;wBAC1C,IAAI,OAAO,EAAE,EAAE;4BACb,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAChC,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC,WAAW,EAAE,WAAW,EAC9C;gCACE,IAAI,OAAO,EAAE;gCACb,YAAY,OAAO,UAAU;gCAC7B;gCACA,OAAO,OAAO,KAAK;gCACnB,YAAY,OAAO,UAAU;gCAC7B,kBAAkB,OAAO,gBAAgB;gCACzC,eAAe,OAAO,aAAa,IAAI;gCACvC,kBAAkB,OAAO,gBAAgB;4BAC3C;4BAEF,QAAQ,IAAI,CAAC;wBACf,OAEK,IAAI,OAAO,UAAU,EAAE;4BAC1B,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAC/B,CAAC,mBAAmB,EAAE,WAAW,EACjC;gCACE,cAAc,OAAO,gBAAgB;gCACrC,YAAY,OAAO,UAAU;gCAC7B,OAAO,OAAO,KAAK;gCACnB,YAAY,OAAO,UAAU;gCAC7B,kBAAkB,OAAO,gBAAgB;gCACzC,eAAe,OAAO,aAAa,IAAI;4BACzC;4BAEF,QAAQ,IAAI,CAAC;wBACf;oBACF,EAAE,OAAO,iBAAsB;wBAC7B,MAAM,aAAa,OAAO,EAAE,IAAI,OAAO,UAAU;wBACjD,QAAQ,KAAK,CACX,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC,EACtC;wBAEF,IAAI,gBAAgB,QAAQ,EAAE;4BAC5B,QAAQ,KAAK,CACX,mCACA,KAAK,SAAS,CAAC,gBAAgB,QAAQ,CAAC,IAAI,EAAE,MAAM;wBAExD;wBACA,gBAAgB,IAAI,CAAC;oBACvB;gBACF;gBAEA,IAAI,gBAAgB,MAAM,GAAG,GAAG;oBAC9B,MAAM,IAAI,MACR,CAAC,mCAAmC,EAAE,gBAAgB,IAAI,CAAC,OAAO;gBAEtE;gBAEA,IAAI,QAAQ,MAAM,GAAG,GAAG;oBACtB,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;wBACf,SACE;wBACF,MAAM;oBACR;oBAEF,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QACA,WAAW;YACT,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SACE;gBACF,MAAM;YACR;YAEF;QACF;QACA,SAAS,CAAC;YACR,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,WACtB,MAAM,QAAQ,EAAE,MAAM,SACtB,MAAM,OAAO,IACb;YACF,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;YAEF,QAAQ,KAAK,CAAC,iBAAiB;gBAC7B,SAAS;gBACT,QAAQ,MAAM,QAAQ,EAAE;gBACxB,MAAM,KAAK,SAAS,CAAC,MAAM,QAAQ,EAAE,MAAM,MAAM;YACnD;QACF;QACA,WAAW;YACT,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,gBAAgB;QACrB,gBAAgB;QAChB,sBAAsB,MAAM,CAAC;IAC/B;IAEA,iEAAiE;IACjE,MAAM,qBAAqB,CAAC;QAC1B,OAAO,UAAU,IAAI,CAAC,CAAC,IACrB,EAAE,eAAe,EAAE,KAAK,CAAC,SAAW,OAAO,cAAc,KAAK;IAElE;IAEA,iEAAiE;IACjE,MAAM,uBAAuB,CAAC;QAC5B,OAAO,SAAS,eAAe,EAAE,KAAK,CAAC,SAAW,OAAO,cAAc,KAAK;IAC9E;IAEA,gEAAgE;IAChE,MAAM,iBAAiB,CAAC;QACtB,MAAM,aAAa,mBAAmB,SAAS,EAAE;QACjD,MAAM,eAAe,qBAAqB;QAE1C,qBACE,8OAAC;YAEC,WAAW,CAAC,sBAAsB,EAChC,aACI,qFACA,kEACJ;;8BAEF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0HAAA,CAAA,QAAK;oCAAC,WAAU;;wCACd,SAAS,KAAK;wCACd,SAAS,UAAU,kBAClB,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;gCAIvC,4BACC,8OAAC;oCAAK,WAAU;;sDACd,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;gCAI1C,8BACC,8OAAC;oCAAK,WAAU;8CAA+I;;;;;;;;;;;;wBAKlK,SAAS,IAAI,kBACZ,8OAAC;4BAAE,WAAW,CAAC,aAAa,EAC1B,aACI,2CACA,yBACJ;sCACC,SAAS,IAAI;;;;;;wBAGjB,MAAM,CAAC,SAAS,EAAE,CAAC,kBAClB,8OAAC;4BAAE,WAAU;sCACV,MAAM,CAAC,SAAS,EAAE,CAAC;;;;;;;;;;;;8BAI1B,8OAAC;oBAAI,WAAU;8BAAQ,oBAAoB;;;;;;;WA3CtC,SAAS,EAAE;;;;;IA8CtB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,QACJ,OAAO,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS,SAAS,KAAK,eAAe,EAAE,GAAG,EAAE;QAExE,OAAQ,SAAS,SAAS;YACxB,KAAK;gBACH,IAAI,SAAS,IAAI,EAAE,SAAS,cAAc;oBACxC,qBACE,8OAAC,6HAAA,CAAA,WAAQ;wBACP,OAAO;wBACP,UAAU,CAAC,IACT,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;wBAE/C,aAAa,SAAS,IAAI,IAAI;wBAC9B,UAAU,SAAS,UAAU;;;;;;gBAGnC;gBACA,qBACE,8OAAC;oBACC,WAAU;oBACV,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oBAC9D,aAAa,SAAS,IAAI,IAAI;oBAC9B,UAAU,SAAS,UAAU;;;;;;YAInC,KAAK;gBACH,qBACE,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oBAC9D,aAAa,SAAS,IAAI,IAAI;oBAC9B,UAAU,SAAS,UAAU;;;;;;YAInC,KAAK;gBACH,qBACE,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,MAAK;oBACL,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oBAC9D,aAAa,SAAS,IAAI,IAAI;oBAC9B,UAAU,SAAS,UAAU;;;;;;YAInC,KAAK;gBACH,qBACE,8OAAC,mIAAA,CAAA,aAAU;oBACT,OAAO;oBACP,eAAe,CAAC,MAAgB,kBAAkB,SAAS,EAAE,EAAE;oBAC/D,UAAU,SAAS,UAAU;8BAE7B,cAAA,8OAAC;wBAAI,WAAU;kCACZ,SAAS,eAAe,EAAE,IAAI,CAAC,QAAQ,sBACtC,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC,mIAAA,CAAA,iBAAc;wCACb,OAAO,OAAO,KAAK;wCACnB,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;;;;;;kDAE3B,8OAAC,0HAAA,CAAA,QAAK;wCACJ,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;wCAC9B,WAAU;kDAET,OAAO,KAAK;;;;;;;+BATP;;;;;;;;;;;;;;;YAiBpB,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACZ,SAAS,eAAe,EAAE,IAAI,CAAC,uBAC9B,8OAAC;4BAAoB,WAAU;;8CAC7B,8OAAC,6HAAA,CAAA,WAAQ;oCACP,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;oCACzB,SAAS,CAAC,SAAS,EAAE,EAAE,QAAQ,CAAC,OAAO,KAAK;oCAC5C,iBAAiB,CAAC;wCAChB,MAAM,gBAAgB,SAAS,EAAE;wCACjC,MAAM,YAAY,UACd;+CAAI;4CAAe,OAAO,KAAK;yCAAC,GAChC,cAAc,MAAM,CAAC,CAAC,IAAc,MAAM,OAAO,KAAK;wCAC1D,kBAAkB,SAAS,EAAE,EAAE;oCACjC;;;;;;8CAEF,8OAAC,0HAAA,CAAA,QAAK;oCACJ,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;oCAC9B,WAAU;8CAET,OAAO,KAAK;;;;;;;2BAhBP,OAAO,EAAE;;;;;;;;;;YAuB3B,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;wBAC9D,aAAa,SAAS,IAAI,IAAI;wBAC9B,UAAU,SAAS,UAAU;;;;;;;;;;;YAKrC,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;wBAC9D,aAAa,SAAS,IAAI,IAAI;wBAC9B,UAAU,SAAS,UAAU;;;;;;;;;;;YAKrC,KAAK;gBACH,qBACE,8OAAC,2IAAA,CAAA,aAAU;oBACT,YAAY,SAAS,EAAE;oBACvB,OAAO;oBACP,UAAU,CAAC,aACT,kBAAkB,SAAS,EAAE,EAAE;oBAEjC,UAAU,SAAS,UAAU;oBAC7B,YAAY,SAAS,KAAK;;;;;;YAIhC;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;;oBAA2E;oBACvE,aAAa,OAAO,CAAC,KAAK,EAAE,YAAY,IAAI,EAAE,GAAG;;;;;;;0BAEnE,8OAAC;gBAAK,UAAU;gBAAc,WAAU;0BACtC,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,UAAU,MAAM,KAAK,kBACpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;mCAGvC,uEAAuE;wBACvE,iBAAiB,GAAG,CAAC,CAAC;4BACpB,IAAI,KAAK,IAAI,KAAK,SAAS;gCACzB,MAAM,QAAQ,KAAK,IAAI;gCACvB,MAAM,iBAAiB,gBAAgB,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE;gCACvD,MAAM,wBAAwB,eAAe,MAAM,CAAC,CAAC,IACnD,iBAAiB,IAAI,CAAC,CAAC,KAAO,GAAG,EAAE,KAAK,EAAE,EAAE;gCAE9C,MAAM,aAAa,cAAc,CAAC,MAAM,EAAE,CAAC;gCAE3C,IAAI,sBAAsB,MAAM,KAAK,GAAG,OAAO;gCAE/C,qBACE,8OAAC;oCAEC,WAAU;;sDAGV,8OAAC;4CACC,WAAU;4CACV,SAAS,IAAM,qBAAqB,MAAM,EAAE;sDAE5C,cAAA,8OAAC;gDAAI,WAAU;;oDACZ,2BACC,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEAE1B,8OAAC;wDAAG,WAAU;kEACX,MAAM,KAAK;;;;;;kEAEd,8OAAC;wDAAK,WAAU;;4DAA2C;4DACvD,sBAAsB,MAAM;4DAAC;4DAC9B,sBAAsB,MAAM,KAAK,IAAI,MAAM;4DAAG;;;;;;;;;;;;;;;;;;wCAMpD,4BACC,8OAAC;4CAAI,WAAU;sDACZ,gBACE,MAAM,CAAC,CAAC,KACP,eAAe,IAAI,CACjB,CAAC,KAAO,GAAG,EAAE,KAAK,GAAG,QAAQ,CAAC,EAAE,GAGnC,GAAG,CAAC,CAAC,8BACJ,8OAAC,uJAAA,CAAA,UAAsB;oDAErB,eAAe;oDACf,qBAAqB;oDACrB,QAAQ;oDACR,WAAU;mDAJL,cAAc,QAAQ,CAAC,EAAE;;;;;;;;;;;mCAnCnC,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;;;;;4BA8C9B,OAAO;gCACL,MAAM,WAAW,KAAK,IAAI;gCAC1B,mDAAmD;gCACnD,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,KAAO,GAAG,EAAE,KAAK,SAAS,EAAE,GAAG;oCACzD,OAAO;gCACT;gCAEA,uDAAuD;gCACvD,MAAM,iBAAiB,gBAAgB,IAAI,CACzC,CAAC,KAAO,GAAG,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE;gCAGxC,IAAI,gBAAgB;oCAClB,qBACE,8OAAC,uJAAA,CAAA,UAAsB;wCAErB,eAAe;wCACf,qBAAqB;wCACrB,QAAQ;wCACR,WAAU;uCAJL,SAAS,EAAE;;;;;gCAOtB;gCAEA,OAAO,eAAe;4BACxB;wBACF;wBAGD,UAAU,MAAM,GAAG,mBAClB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,SAAS;oCACT,UAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,UAAU;8CAET,eAAe,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C", "debugId": null}}, {"offset": {"line": 3145, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/app/edit-submission/%5BhashedId%5D/%5BsubmissionId%5D/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useParams } from \"next/navigation\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport axios from \"@/lib/axios\";\r\nimport { decode } from \"@/lib/encodeDecode\";\r\nimport { fetchQuestions } from \"@/lib/api/form-builder\";\r\nimport { Question } from \"@/types/formBuilder\";\r\nimport { Submission } from \"@/app/(main)/project/[hashedId]/data/table/columns\";\r\nimport Spinner from \"@/components/general/Spinner\";\r\nimport { EditForm } from \"@/components/form-preview/editForm\";\r\n\r\nconst fetchSubmissions = async (projectId: number, submissionId: number) => {\r\n  const { data } = await axios.get(`/form-submissions/${projectId}`);\r\n  const submissions = data.data.formSubmissions as Submission[];\r\n  const submission = submissions.find((s) => s.id === submissionId);\r\n  if (!submission) {\r\n    throw new Error(\"Submission not found\");\r\n  }\r\n  return submission;\r\n};\r\n\r\nexport default function EditSubmissionPage() {\r\n  const { hashedId, submissionId } = useParams();\r\n  const projectId = decode(hashedId as string);\r\n  const parsedSubmissionId = Number(submissionId);\r\n\r\n  // Validate projectId and submissionId\r\n  if (projectId === null || isNaN(parsedSubmissionId)) {\r\n    return <div>Error: Invalid project or submission ID.</div>;\r\n  }\r\n\r\n  const {\r\n    data: questions = [],\r\n    isLoading: questionsLoading,\r\n    isError: questionsError,\r\n  } = useQuery<Question[]>({\r\n    queryKey: [\"questions\", projectId],\r\n    queryFn: () => fetchQuestions({ projectId }),\r\n    enabled: !!projectId,\r\n  });\r\n\r\n  const {\r\n    data: submission,\r\n    isLoading: submissionLoading,\r\n    isError: submissionError,\r\n    refetch: refetchSubmission,\r\n  } = useQuery<Submission>({\r\n    queryKey: [\"submission\", projectId, parsedSubmissionId],\r\n    queryFn: () => fetchSubmissions(projectId, parsedSubmissionId),\r\n    enabled: !!projectId && !!parsedSubmissionId,\r\n  });\r\n\r\n  if (questionsLoading || submissionLoading) {\r\n    return <Spinner />;\r\n  }\r\n\r\n  if (questionsError || submissionError || !questions || !submission) {\r\n    return (\r\n      <p className=\"text-sm text-red-500\">\r\n        Error loading submission or form. Please try again.\r\n      </p>\r\n    );\r\n  }\r\n\r\n  const handleSave = () => {\r\n    // Notify table to refetch if there's an opener window\r\n    if (window.opener) {\r\n      window.opener.postMessage({ type: \"REFETCH_SUBMISSIONS\" }, \"*\");\r\n    }\r\n    // Refetch the submission data to update the UI\r\n    refetchSubmission();\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6\">\r\n      <EditForm\r\n        questions={questions}\r\n        submission={submission}\r\n        projectId={projectId}\r\n        submissionId={parsedSubmissionId}\r\n        onSave={handleSave}\r\n      />\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAGA;AACA;AAVA;;;;;;;;;AAYA,MAAM,mBAAmB,OAAO,WAAmB;IACjD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW;IACjE,MAAM,cAAc,KAAK,IAAI,CAAC,eAAe;IAC7C,MAAM,aAAa,YAAY,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;IACpD,IAAI,CAAC,YAAY;QACf,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEe,SAAS;IACtB,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAC3C,MAAM,YAAY,CAAA,GAAA,mHAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,qBAAqB,OAAO;IAElC,sCAAsC;IACtC,IAAI,cAAc,QAAQ,MAAM,qBAAqB;QACnD,qBAAO,8OAAC;sBAAI;;;;;;IACd;IAEA,MAAM,EACJ,MAAM,YAAY,EAAE,EACpB,WAAW,gBAAgB,EAC3B,SAAS,cAAc,EACxB,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAc;QACvB,UAAU;YAAC;YAAa;SAAU;QAClC,SAAS,IAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE;YAAU;QAC1C,SAAS,CAAC,CAAC;IACb;IAEA,MAAM,EACJ,MAAM,UAAU,EAChB,WAAW,iBAAiB,EAC5B,SAAS,eAAe,EACxB,SAAS,iBAAiB,EAC3B,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAc;QACvB,UAAU;YAAC;YAAc;YAAW;SAAmB;QACvD,SAAS,IAAM,iBAAiB,WAAW;QAC3C,SAAS,CAAC,CAAC,aAAa,CAAC,CAAC;IAC5B;IAEA,IAAI,oBAAoB,mBAAmB;QACzC,qBAAO,8OAAC,iIAAA,CAAA,UAAO;;;;;IACjB;IAEA,IAAI,kBAAkB,mBAAmB,CAAC,aAAa,CAAC,YAAY;QAClE,qBACE,8OAAC;YAAE,WAAU;sBAAuB;;;;;;IAIxC;IAEA,MAAM,aAAa;QACjB,sDAAsD;QACtD,IAAI,OAAO,MAAM,EAAE;YACjB,OAAO,MAAM,CAAC,WAAW,CAAC;gBAAE,MAAM;YAAsB,GAAG;QAC7D;QACA,+CAA+C;QAC/C;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0IAAA,CAAA,WAAQ;YACP,WAAW;YACX,YAAY;YACZ,WAAW;YACX,cAAc;YACd,QAAQ;;;;;;;;;;;AAIhB", "debugId": null}}]}